-- Supabase Database Schema for Math Adaptive Test
-- This file contains the SQL schema needed to set up the database tables in Supabase

-- Enable necessary extensions
create extension if not exists "uuid-ossp";

-- User profiles table (extends Supabase auth.users)
create table public.user_profiles (
  id uuid references auth.users on delete cascade not null primary key,
  email text not null,
  full_name text,
  avatar_url text,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null,
  
  constraint user_profiles_email_check check (char_length(email) >= 3)
);

-- Test sessions table
create table public.test_sessions (
  id text not null primary key,
  user_id uuid references public.user_profiles(id) on delete cascade not null,
  session_id text not null,
  mode text not null check (mode in ('test', 'practice')),
  max_items integer not null check (max_items >= 1 and max_items <= 100),
  target_subjects text[] default null,
  show_solution boolean not null default false,
  time_limit integer default null check (time_limit is null or (time_limit >= 1 and time_limit <= 240)),
  start_time timestamp with time zone not null,
  current_ability numeric[] not null default '{0,0,0,0,0,0}',
  answered_items text[] not null default '{}',
  responses jsonb not null default '[]'::jsonb,
  recent_performance integer[] not null default '{}',
  subject_question_count jsonb not null default '{}'::jsonb,
  status text not null default 'active' check (status in ('active', 'completed', 'abandoned')),
  completed_at timestamp with time zone default null,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null,
  
  -- Ensure unique session per user
  constraint test_sessions_user_session_unique unique (user_id, session_id)
);

-- Indexes for better performance
create index test_sessions_user_id_idx on public.test_sessions(user_id);
create index test_sessions_session_id_idx on public.test_sessions(session_id);
create index test_sessions_status_idx on public.test_sessions(status);
create index test_sessions_created_at_idx on public.test_sessions(created_at desc);

-- Row Level Security (RLS) policies
alter table public.user_profiles enable row level security;
alter table public.test_sessions enable row level security;

-- User profiles policies
create policy "Public profiles are viewable by everyone." on public.user_profiles
  for select using (true);

create policy "Users can insert their own profile." on public.user_profiles
  for insert with check (auth.uid() = id);

create policy "Users can update own profile." on public.user_profiles
  for update using (auth.uid() = id);

-- Test sessions policies
create policy "Users can view own sessions." on public.test_sessions
  for select using (auth.uid() = user_id);

create policy "Users can insert own sessions." on public.test_sessions
  for insert with check (auth.uid() = user_id);

create policy "Users can update own sessions." on public.test_sessions
  for update using (auth.uid() = user_id);

create policy "Users can delete own sessions." on public.test_sessions
  for delete using (auth.uid() = user_id);

-- Functions for updated_at timestamps
create or replace function public.handle_updated_at()
returns trigger
language plpgsql
security definer set search_path = public
as $$
begin
  new.updated_at = now();
  return new;
end;
$$;

-- Triggers for updated_at
create trigger handle_updated_at 
  before update on public.user_profiles
  for each row execute procedure public.handle_updated_at();

create trigger handle_updated_at 
  before update on public.test_sessions
  for each row execute procedure public.handle_updated_at();

-- Function to handle new user registration
create or replace function public.handle_new_user()
returns trigger
language plpgsql
security definer set search_path = public
as $$
begin
  insert into public.user_profiles (id, email, full_name, avatar_url)
  values (
    new.id,
    new.email,
    new.raw_user_meta_data->>'full_name',
    new.raw_user_meta_data->>'avatar_url'
  );
  return new;
end;
$$;

-- Trigger for new user registration
create trigger on_auth_user_created
  after insert on auth.users
  for each row execute procedure public.handle_new_user();

-- Grant necessary permissions to authenticated users
grant usage on schema public to anon, authenticated;
grant all on all tables in schema public to anon, authenticated;
grant all on all sequences in schema public to anon, authenticated;
grant all on all functions in schema public to anon, authenticated;

-- Example data (for testing)
-- Note: This will only work after users are created through authentication
/*
-- Insert sample user profile (replace with actual user ID from auth.users)
insert into public.user_profiles (id, email, full_name, avatar_url)
values (
  '00000000-0000-0000-0000-000000000000',
  '<EMAIL>',
  'Test User',
  null
);

-- Insert sample test session
insert into public.test_sessions (
  id,
  user_id,
  session_id,
  mode,
  max_items,
  show_solution,
  start_time,
  subject_question_count
) values (
  '00000000-0000-0000-0000-000000000000_sample',
  '00000000-0000-0000-0000-000000000000',
  'sample-session-id',
  'practice',
  20,
  true,
  now(),
  '{"algebra": 0, "geometry": 0, "number_theory": 0, "prealgebra": 0, "counting_and_probability": 0, "intermediate_algebra": 0}'::jsonb
);
*/