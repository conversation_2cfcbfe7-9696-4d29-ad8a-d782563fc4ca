// math-adaptive-test/app/test/page.tsx

'use client';

import { useEffect, useState, useRef, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Clock,
  Brain,
  CheckCircle2,
  XCircle,
  AlertCircle,
  SkipForward,
  Eye,
  EyeOff,
  Calculator,
  BookOpen,
  TrendingUp,
  TrendingDown,
  Target,
  BarChart3,
  HelpCircle,
  Send
} from 'lucide-react';
// Import required CSS for LaTeX
import 'katex/dist/katex.min.css';
import { Trophy } from 'lucide-react';

// Import shared LaTeX rendering components
import { MixedContent } from '@/components/latex-renderer';

// Import shared problem loading logic
import {
  loadNextAdaptiveProblem,
  submitAnswer,
  ProblemResponse,
  SubmitAnswerResponse
} from '@/lib/problem-loader';

// Import session access check function
import { hasSessionAccess } from '@/app/api/test/session-store';






// Types
interface TestSession {
  sessionId: string;
  config: {
    mode: 'test' | 'practice';
    maxItems: number;
    showSolution: boolean;
    timeLimit: number | null;
    questionIndex?: number; // For sequential mode
  };
  progress: {
    completed: number;
    total: number;
    correctCount: number;
    accuracy: number;
  };
  currentAbility: number[];
  answeredItems: string[]; // <--- 添加此行
  subjectQuestionCount: Record<string, number>;
  recentPerformance: number[];
}

// Use shared ProblemResponse interface
type Problem = ProblemResponse;

// Use shared SubmitAnswerResponse interface
type SubmitResponse = SubmitAnswerResponse;

// Subject display names
const SUBJECT_NAMES: Record<string, string> = {
  'algebra': 'Algebra',
  'counting_and_probability': 'Counting & Probability',
  'geometry': 'Geometry',
  'intermediate_algebra': 'Intermediate Algebra',
  'number_theory': 'Number Theory',
  'prealgebra': 'Pre-algebra'
};

// Subject colors
const SUBJECT_COLORS: Record<string, string> = {
  'algebra': '#FF6B6B',
  'counting_and_probability': '#4ECDC4',
  'geometry': '#45B7D1',
  'intermediate_algebra': '#96CEB4',
  'number_theory': '#FFEAA7',
  'prealgebra': '#DDA0DD'
};





// Component that safely uses searchParams
function TestContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { user } = useAuth(); // Get current user

  // State
  const [session, setSession] = useState<TestSession | null>(null);
  const [currentProblem, setCurrentProblem] = useState<Problem | null>(null);
  const [userAnswer, setUserAnswer] = useState('');
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [showSolution, setShowSolution] = useState(false);
  const [lastResult, setLastResult] = useState<SubmitResponse | null>(null);
  const [timeSpent, setTimeSpent] = useState(0);
  const [totalTime, setTotalTime] = useState(0);
  const [remainingTime, setRemainingTime] = useState<number | null>(null);
  const [isTimeExpired, setIsTimeExpired] = useState(false);
  const [showQuitDialog, setShowQuitDialog] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentQuestionNumber, setCurrentQuestionNumber] = useState(1);

  // Refs
  const startTimeRef = useRef<number>(0);
  const questionStartTimeRef = useRef<number>(Date.now());
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize test
  useEffect(() => {
    initializeTest();
  }, []);

  // Separate effect for timers to handle state dependencies properly
  useEffect(() => {
    // Start timers
    timerRef.current = setInterval(() => {
      const currentTotalTime = Math.floor((Date.now() - startTimeRef.current) / 1000);
      setTotalTime(currentTotalTime);
      setTimeSpent(Math.floor((Date.now() - questionStartTimeRef.current) / 1000));

      // Update countdown timer if there's a time limit
      setRemainingTime(prevRemainingTime => {
        if (prevRemainingTime !== null && prevRemainingTime > 0) {
          const newRemainingTime = Math.max(0, prevRemainingTime - 1);
          
          // Check if time has expired
          if (newRemainingTime === 0 && !isTimeExpired) {
            setIsTimeExpired(true);
          }
          
          return newRemainingTime;
        }
        return prevRemainingTime;
      });
    }, 1000);

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isTimeExpired]);

  // Handle time expiration
  useEffect(() => {
    if (isTimeExpired && session) {
      // When time expires, end the test immediately
      setTimeout(() => {
        router.push(`/results?sessionId=${session.sessionId}&timeExpired=true`);
      }, 2000); // Give user 2 seconds to see the time expired message
    }
  }, [isTimeExpired, session, router]);

  const initializeTest = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get test mode from URL params
      const mode = searchParams.get('mode') || 'test';
      const maxItems = parseInt(searchParams.get('questions') || '20');
      const timeLimit = searchParams.get('timeLimit') ? parseInt(searchParams.get('timeLimit')!) : null;
      const sessionId = searchParams.get('sessionId');
      const questionIndex = searchParams.get('question') ? parseInt(searchParams.get('question')!) : null;

      let testSession: TestSession;

      if (sessionId) {
        // For anonymous sessions, try localStorage first before checking server
        let foundLocalSession = false;
        if (!user?.id) {
          try {
            const localSessionData = localStorage.getItem(`test-session-${sessionId}`);
            if (localSessionData) {
              console.log('Found session in localStorage');
              const parsedSession = JSON.parse(localSessionData);
              testSession = parsedSession;
              foundLocalSession = true;
            }
          } catch (error) {
            console.warn('Failed to load session from localStorage:', error);
          }
        }

        // If we didn't find it locally or user is authenticated, try server
        if (!foundLocalSession) {
          // Check if user has access to this session (server-side check)
          const hasAccess = await hasSessionAccess(sessionId, user?.id);
          if (!hasAccess) {
            throw new Error('You do not have permission to access this session');
          }

          // Resume existing session - this will load from database if not in cache
          const response = await fetch(`/api/test/start?sessionId=${sessionId}`);
          if (!response.ok) {
            const errorData = await response.json();
            // If session not found on server but user is anonymous, try localStorage again
            if (errorData.code === 'SESSION_NOT_FOUND' && !user?.id) {
              console.log('Session not found on server, trying localStorage again for anonymous user');
              try {
                const localSessionData = localStorage.getItem(`test-session-${sessionId}`);
                if (localSessionData) {
                  const parsedSession = JSON.parse(localSessionData);
                  testSession = parsedSession;
                  console.log('Successfully loaded session from localStorage as fallback');
                } else {
                  throw new Error('Session not found on server or in localStorage');
                }
              } catch (localError) {
                throw new Error('Session not found on server or in localStorage');
              }
            } else {
              throw new Error(errorData.error || 'Failed to load session');
            }
          } else {
            const sessionData = await response.json();

            // Convert API response to TestSession format
            testSession = {
              sessionId: sessionData.sessionId,
              config: {
                mode: sessionData.config.mode,
                maxItems: sessionData.config.maxItems,
                showSolution: sessionData.config.showSolution,
                timeLimit: sessionData.config.timeLimit,
                questionIndex
              },
              progress: sessionData.progress,
              currentAbility: sessionData.performance.currentAbility,
              answeredItems: sessionData.data.answeredItems || [],
              subjectQuestionCount: sessionData.performance.subjectQuestionCount,
              recentPerformance: sessionData.performance.recentPerformance
            };
          }
        }
      } else {
        // Start new session
        const response = await fetch('/api/test/start', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            userId: user?.id, // Pass current user ID if logged in
            config: {
              mode,
              maxItems,
              showSolution: mode === 'practice',
              timeLimit,
              questionIndex
            }
          })
        });

        if (!response.ok) throw new Error('Failed to start test');
        const data = await response.json();
        testSession = {
          sessionId: data.sessionId,
          config: { ...data.config, questionIndex },
          progress: {
            completed: 0,
            total: maxItems,
            correctCount: 0,
            accuracy: 0
          },
          currentAbility: data.session.currentAbility,
          answeredItems: [],
          subjectQuestionCount: data.session.subjectQuestionCount,
          recentPerformance: []
        };
      }

      // Save anonymous sessions to localStorage
      if (!user?.id) {
        try {
          localStorage.setItem(`test-session-${testSession.sessionId}`, JSON.stringify(testSession));
          console.log('Saved session to localStorage for anonymous user');
        } catch (error) {
          console.warn('Failed to save session to localStorage:', error);
        }
      }

      // Initialize countdown timer if there's a time limit
      if (testSession.config.timeLimit) {
        const totalTimeInSeconds = testSession.config.timeLimit * 60;
        
        if (sessionId) {
          // For resumed sessions, we need to calculate remaining time
          // This is a simplified approach - in production, you'd want to store the start time
          const elapsedTime = Math.floor((Date.now() - startTimeRef.current) / 1000);
          const remainingSeconds = Math.max(0, totalTimeInSeconds - elapsedTime);
          setRemainingTime(remainingSeconds);
        } else {
          // For new sessions, use full time limit
          setRemainingTime(totalTimeInSeconds);
        }
      }

      // Set the start time when the test session is initialized
      startTimeRef.current = Date.now();

      // Set the current question number based on progress
      const questionNumber = testSession.progress.completed + 1;
      setCurrentQuestionNumber(questionNumber);
      
      // Sync URL parameter with current question number and sessionId
      const currentParams = new URLSearchParams(searchParams);
      if (currentParams.get('question') !== questionNumber.toString()) {
        currentParams.set('question', questionNumber.toString());
      }
      // Always ensure sessionId is in URL
      if (currentParams.get('sessionId') !== testSession.sessionId) {
        currentParams.set('sessionId', testSession.sessionId);
      }
      router.replace(`/test?${currentParams.toString()}`);

      setSession(testSession);
      await loadNextProblem(testSession);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to initialize test');
    } finally {
      setLoading(false);
    }
  };

  const loadNextProblem = async (testSession: TestSession) => {
    try {
      setLoading(true);
      setShowSolution(false);
      setLastResult(null);
      setUserAnswer('');

      // Calculate current question index for sequential mode
      const currentQuestionIndex = testSession.config.questionIndex
        ? testSession.config.questionIndex + testSession.progress.completed
        : undefined;

      // Use shared problem loading function
      const nextItem = await loadNextAdaptiveProblem(
        testSession.currentAbility,
        testSession.answeredItems,
        testSession.config.mode,
        testSession.recentPerformance,
        testSession.subjectQuestionCount,
        currentQuestionIndex
      );

      if (!nextItem) {
        // No more problems, go to results
        router.push(`/results?sessionId=${testSession.sessionId}`);
        return;
      }

      setCurrentProblem(nextItem);
      questionStartTimeRef.current = Date.now();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load problem');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (skipQuestion: boolean = false) => {
    if (!session || !currentProblem || submitting) return;

    if (!skipQuestion && !userAnswer.trim()) {
      setError('Please enter an answer');
      return;
    }

    try {
      setSubmitting(true);
      setError(null);

      // Use shared submit answer function
      const result: SubmitResponse = await submitAnswer(
        session.sessionId,
        currentProblem.itemId,
        skipQuestion ? '' : userAnswer.trim(),
        timeSpent
      );
      setLastResult(result);

      // Update session with new progress
      const updatedSession: TestSession = {
        ...session,
        progress: result.progress,
        currentAbility: result.updatedAbility,
        answeredItems: [...session.answeredItems, currentProblem.itemId], // <--- 添加此行
        recentPerformance: [
          ...session.recentPerformance.slice(-9),
          result.correct ? 1 : 0
        ]
      };

      // Update subject question count
      if (currentProblem.subject in updatedSession.subjectQuestionCount) {
        updatedSession.subjectQuestionCount[currentProblem.subject]++;
      }

      setSession(updatedSession);
      
      // Save updated session to localStorage for anonymous users
      if (!user?.id) {
        try {
          localStorage.setItem(`test-session-${updatedSession.sessionId}`, JSON.stringify(updatedSession));
          console.log('Updated session saved to localStorage');
        } catch (error) {
          console.warn('Failed to save updated session to localStorage:', error);
        }
      }

      // Check if test is complete
      if (result.progress.completed >= session.config.maxItems) {
        setTimeout(() => {
          router.push(`/results?sessionId=${session.sessionId}`);
        }, 2000);
      } else if (!skipQuestion && session.config.mode === 'practice' && !result.correct) {
        // In practice mode, show solution for incorrect answers
        setShowSolution(true);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to submit answer');
    } finally {
      setSubmitting(false);
    }
  };

  const handleNext = () => {
    if (session) {
      const newQuestionNumber = currentQuestionNumber + 1;
      setCurrentQuestionNumber(newQuestionNumber);
      
      // Update URL parameter to reflect current question number and maintain sessionId
      const currentParams = new URLSearchParams(searchParams);
      currentParams.set('question', newQuestionNumber.toString());
      if (session.sessionId && currentParams.get('sessionId') !== session.sessionId) {
        currentParams.set('sessionId', session.sessionId);
      }
      router.replace(`/test?${currentParams.toString()}`);
      
      loadNextProblem(session);
    }
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getSubjectStats = (): Array<{ subject: string; count: number; color: string }> => {
    if (!session) return [];

    return Object.entries(session.subjectQuestionCount)
      .map(([subject, count]) => ({
        subject: SUBJECT_NAMES[subject] || subject,
        count,
        color: SUBJECT_COLORS[subject] || '#gray'
      }))
      .sort((a, b) => b.count - a.count);
  };

  const getRecentPerformanceIcon = () => {
    if (!session || session.recentPerformance.length < 3) return null;

    const recentRate = session.recentPerformance.slice(-3).reduce((a, b) => a + b, 0) / 3;

    if (recentRate < 0.3) {
      return <TrendingDown className="w-5 h-5 text-red-500" />;
    } else if (recentRate > 0.8) {
      return <TrendingUp className="w-5 h-5 text-green-500" />;
    }
    return null;
  };

  if (loading && !currentProblem) {
    return (
      <div className="container mx-auto p-6 flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Brain className="w-12 h-12 animate-pulse mx-auto mb-4" />
          <p className="text-lg">Loading test...</p>
        </div>
      </div>
    );
  }

  if (error && !session) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <Button onClick={() => router.push('/')} className="mt-4">
          Return to Home
        </Button>
      </div>
    );
  }

  if (!session || !currentProblem) return null;

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              {session.config.mode === 'practice' ? (
                <>
                  <BookOpen className="w-6 h-6" />
                  Practice Mode
                  {session.config.questionIndex && (
                    <Badge variant="outline" className="ml-2">Sequential</Badge>
                  )}
                </>
              ) : (
                <>
                  <Brain className="w-6 h-6" />
                  Formal Test
                  {session.config.questionIndex && (
                    <Badge variant="outline" className="ml-2">Sequential</Badge>
                  )}
                </>
              )}
            </h1>
            <p className="text-gray-600">
              {session.config.questionIndex ? (
                <>Question {session.config.questionIndex + session.progress.completed} (Sequential Mode)</>
              ) : (
                <>Question {currentQuestionNumber} of {session.config.maxItems}</>
              )}
            </p>
          </div>

          <div className="flex items-center gap-4">
            {remainingTime !== null ? (
              <div className="text-right">
                <p className="text-sm text-gray-600">Time Remaining</p>
                <p className={`text-lg font-mono ${remainingTime < 300 ? 'text-red-500' : remainingTime < 600 ? 'text-yellow-500' : ''}`}>
                  {formatTime(remainingTime)}
                </p>
              </div>
            ) : (
              <div className="text-right">
                <p className="text-sm text-gray-600">Total Time</p>
                <p className="text-lg font-mono">{formatTime(totalTime)}</p>
              </div>
            )}
            
            {/* User Login Icon */}
            {user ? (
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                  {user.email?.charAt(0).toUpperCase() || 'U'}
                </div>
                <span className="text-sm text-gray-600">
                  {user.email?.split('@')[0] || 'User'}
                </span>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center text-white text-sm">
                  ?
                </div>
                <span className="text-sm text-gray-600">Anonymous</span>
              </div>
            )}
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowQuitDialog(true)}
              disabled={isTimeExpired}
            >
              End Test
            </Button>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="space-y-2">
          <Progress
            value={(session.progress.completed / session.config.maxItems) * 100}
            className="h-3"
          />
          <div className="flex justify-between text-sm text-gray-600">
            <span>Progress: {Math.round((session.progress.completed / session.config.maxItems) * 100)}%</span>
            <span>Accuracy: {session.progress.accuracy}% ({session.progress.correctCount}/{session.progress.completed})</span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Question Area */}
        <div className="lg:col-span-2 space-y-4">
          {/* Question Card */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div
                    className="w-4 h-4 rounded"
                    style={{ backgroundColor: SUBJECT_COLORS[currentProblem.subject] }}
                  />
                  <CardTitle>{SUBJECT_NAMES[currentProblem.subject] || currentProblem.subject}</CardTitle>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">Level {currentProblem.difficulty.toString()}</Badge>
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    {formatTime(timeSpent)}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="prose prose-lg max-w-none">
                <MixedContent content={currentProblem.problem} />
              </div>
            </CardContent>
          </Card>

          {/* Answer Input */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Your Answer</CardTitle>
              <CardDescription>
                <MixedContent content="Enter your answer below. Fractions can be entered as $1/2$ or $\frac{1}{2}$." />
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  value={userAnswer}
                  onChange={(e) => setUserAnswer(e.target.value)}
                  placeholder="Type your answer here..."
                  className="text-lg"
                  disabled={submitting || isTimeExpired || (lastResult !== null && session.progress.completed < session.config.maxItems)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter' && !submitting && !isTimeExpired) {
                      handleSubmit();
                    }
                  }}
                />
                <Button
                  onClick={() => handleSubmit()}
                  disabled={submitting || isTimeExpired || (lastResult !== null && session.progress.completed < session.config.maxItems)}
                  className="min-w-[100px]"
                >
                  {submitting ? 'Checking...' : 'Submit'}
                  <Send className="w-4 h-4 ml-2" />
                </Button>
              </div>

              {/* Time Expired Warning */}
              {isTimeExpired && (
                <Alert variant="destructive">
                  <Clock className="h-4 w-4" />
                  <AlertTitle>Time Expired</AlertTitle>
                  <AlertDescription>
                    The time limit has been reached. You can no longer submit answers.
                  </AlertDescription>
                </Alert>
              )}

              {/* Action Buttons */}
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleSubmit(true)}
                  disabled={submitting || isTimeExpired || (lastResult !== null && session.progress.completed < session.config.maxItems)}
                >
                  <SkipForward className="w-4 h-4 mr-2" />
                  Skip Question
                </Button>

                {session.config.mode === 'practice' && currentProblem.solution && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowSolution(!showSolution)}
                  >
                    {showSolution ? (
                      <>
                        <EyeOff className="w-4 h-4 mr-2" />
                        Hide Solution
                      </>
                    ) : (
                      <>
                        <Eye className="w-4 h-4 mr-2" />
                        Show Solution
                      </>
                    )}
                  </Button>
                )}
              </div>

              {/* Result Feedback */}
              {lastResult && (
                <Alert className={lastResult.correct ? 'border-green-500' : 'border-red-500'}>
                  {lastResult.correct ? (
                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-500" />
                  )}
                  <AlertTitle>{lastResult.correct ? 'Correct!' : 'Incorrect'}</AlertTitle>
                  {!lastResult.correct && lastResult.correctAnswer && (
                    <AlertDescription>
                     The correct answer is: <strong><MixedContent content={`$${lastResult.correctAnswer}$`} /></strong>
                    </AlertDescription>
                  )}
                </Alert>
              )}

              {/* Continue Button */}
              {lastResult && session.progress.completed < session.config.maxItems && (
                <Button
                  onClick={handleNext}
                  className="w-full"
                  size="lg"
                >
                  Next Question →
                </Button>
              )}

              {/* Test Complete Message */}
              {lastResult && session.progress.completed >= session.config.maxItems && (
                <Alert>
                  <Trophy className="h-4 w-4" />
                  <AlertTitle>Test Complete!</AlertTitle>
                  <AlertDescription>
                    Redirecting to results page...
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* Solution Card (Practice Mode) */}
          {showSolution && currentProblem.solution && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="w-5 h-5" />
                  Solution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose prose-sm max-w-none space-y-4">
                  <MixedContent content={currentProblem.solution} />
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-4">
          {/* Performance Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                Performance
                {getRecentPerformanceIcon()}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-600">Overall Accuracy</span>
                  <span className="text-sm font-medium">{session.progress.accuracy}%</span>
                </div>
                <Progress value={session.progress.accuracy} className="h-2" />
              </div>

              {session.config.mode === 'practice' && session.recentPerformance.length >= 3 && (
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm text-gray-600">Recent Performance</span>
                    <span className="text-sm font-medium">
                      {Math.round(session.recentPerformance.slice(-3).reduce((a, b) => a + b, 0) / 3 * 100)}%
                    </span>
                  </div>
                  <div className="flex gap-1">
                    {session.recentPerformance.slice(-5).map((correct, idx) => (
                      <div
                        key={idx}
                        className={`flex-1 h-6 rounded ${correct ? 'bg-green-500' : 'bg-red-500'}`}
                      />
                    ))}
                  </div>
                  <p className="text-xs text-gray-600 mt-1">
                    Last {Math.min(session.recentPerformance.length, 5)} question{Math.min(session.recentPerformance.length, 5) === 1 ? '' : 's'}
                  </p>
                </div>
              )}

              {/* Difficulty Adjustment Message */}
              {session.config.mode === 'practice' && session.recentPerformance.length >= 3 && (
                <Alert className="py-2">
                  <AlertDescription className="text-sm">
                    {(() => {
                      const rate = session.recentPerformance.slice(-3).reduce((a, b) => a + b, 0) / 3;
                      if (rate < 0.3) return '💡 Difficulty decreased';
                      if (rate > 0.8) return '🎯 Difficulty increased';
                      return '📊 Adaptive difficulty active';
                    })()}
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* Subject Distribution */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Target className="w-5 h-5" />
                Subject Distribution
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {getSubjectStats().map(({ subject, count, color }) => (
                  <div key={subject} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div
                        className="w-3 h-3 rounded"
                        style={{ backgroundColor: color }}
                      />
                      <span className="text-sm">{subject}</span>
                    </div>
                    <Badge variant="secondary">{count}</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Tips */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <HelpCircle className="w-5 h-5" />
                Tips
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• Read each question carefully</li>
                <li>• Use proper fraction notation</li>
                <li>• Skip if you're unsure</li>
                {session.config.mode === 'practice' && (
                  <>
                    <li>• View solutions to learn</li>
                    <li>• System adapts to your level</li>
                  </>
                )}
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Quit Dialog */}
      <Dialog open={showQuitDialog} onOpenChange={setShowQuitDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>End Test Early?</DialogTitle>
            <DialogDescription>
              Are you sure you want to end the test? You've completed {session.progress.completed} out of {session.config.maxItems} questions.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowQuitDialog(false)}>
              Continue Test
            </Button>
            <Button onClick={() => router.push(`/results?sessionId=${session.sessionId}`)}>
              End Test
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Main component with Suspense boundary
export default function TestPage() {
  return (
    <Suspense fallback={
      <div className="container mx-auto p-6 flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Brain className="w-12 h-12 animate-pulse mx-auto mb-4" />
          <p className="text-lg">Loading test...</p>
        </div>
      </div>
    }>
      <TestContent />
    </Suspense>
  );
}

