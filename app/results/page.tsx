// math-adaptive-test/app/results/page.tsx

'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  ArcElement
} from 'chart.js';
import { Bar, Radar, Doughnut } from 'react-chartjs-2';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Trophy,
  TrendingUp,
  AlertCircle,
  TrendingDown,
  Clock,
  Target,
  Brain,
  FileText,
  Download,
  Share2,
  <PERSON>fresh<PERSON><PERSON>,
  CheckCircle2,
  XCircle
} from 'lucide-react';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  ArcElement
);

// Types
interface TestResults {
  sessionId: string;
  status: string;
  config: {
    mode: 'test' | 'practice';
    maxItems: number;
    targetSubjects: string[] | null;
    timeLimit: number | null;
  };
  summary: {
    totalQuestions: number;
    correctAnswers: number;
    accuracy: number;
    avgTimePerQuestion: number;
    totalTime: number;
    completedAt: string;
  };
  ability: {
    final: number[];
    initial: number[];
    changes: number[];
  };
  subjectScores: Record<string, {
    standardizedScore: number;
    percentageScore: number;
    questionsAttempted: number;
    correctAnswers: number;
    accuracy: number;
    avgDifficulty: number;
  }>;
  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
  detailedResponses: Array<{
    itemId: string;
    subject: string;
    difficulty: number;
    correct: boolean;
    timeSpent: number;
    timestamp: string;
  }>;
}

// Subject display names
const SUBJECT_NAMES: Record<string, string> = {
  'algebra': 'Algebra',
  'counting_and_probability': 'Counting & Probability',
  'geometry': 'Geometry',
  'intermediate_algebra': 'Intermediate Algebra',
  'number_theory': 'Number Theory',
  'prealgebra': 'Pre-algebra'
};

// Subject colors
const SUBJECT_COLORS: Record<string, string> = {
  'algebra': '#FF6B6B',
  'counting_and_probability': '#4ECDC4',
  'geometry': '#45B7D1',
  'intermediate_algebra': '#96CEB4',
  'number_theory': '#FFEAA7',
  'prealgebra': '#DDA0DD'
};

// Component that safely uses searchParams
function ResultsContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const sessionId = searchParams.get('sessionId');

  const [results, setResults] = useState<TestResults | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    if (!sessionId) {
      setError('No session ID provided');
      setLoading(false);
      return;
    }

    fetchResults();
  }, [sessionId]);

  const fetchResults = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/test/results?sessionId=${sessionId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch results');
      }

      const data = await response.json();
      setResults(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getPerformanceLevel = (score: number): { label: string; badgeClass: string } => {
    if (score >= 600) return {
      label: 'Excellent',
      badgeClass: 'bg-green-100 text-green-800 border-green-200'
    };
    if (score >= 550) return {
      label: 'Good',
      badgeClass: 'bg-blue-100 text-blue-800 border-blue-200'
    };
    if (score >= 500) return {
      label: 'Average',
      badgeClass: 'bg-yellow-100 text-yellow-800 border-yellow-200'
    };
    if (score >= 450) return {
      label: 'Below Average',
      badgeClass: 'bg-orange-100 text-orange-800 border-orange-200'
    };
    return {
      label: 'Needs Improvement',
      badgeClass: 'bg-red-100 text-red-800 border-red-200'
    };
  };

  const downloadReport = () => {
    // Generate PDF or CSV report
    const reportData = {
      sessionId: results?.sessionId,
      date: new Date().toISOString(),
      results: results
    };
    
    const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `math-test-results-${sessionId}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6 flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="w-12 h-12 animate-spin mx-auto mb-4" />
          <p className="text-lg">Loading your results...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <Button onClick={() => router.push('/')} className="mt-4">
          Return to Home
        </Button>
      </div>
    );
  }

  if (!results) {
    return null;
  }

  // Calculate overall score based on weighted average of standardized scores
  // But also ensure it reflects the actual accuracy
  const subjectScoreValues = Object.values(results.subjectScores);
  const avgStandardizedScore = subjectScoreValues.length > 0
    ? Math.round(subjectScoreValues.reduce((sum, score) => sum + score.standardizedScore, 0) / subjectScoreValues.length)
    : 200; // Default minimum score when no subjects

  // Use the lower of standardized score or accuracy-based score to be more realistic
  const accuracyBasedScore = Math.round(200 + (results.summary.accuracy / 100) * 600);
  const overallScore = Math.min(avgStandardizedScore, accuracyBasedScore);

  const performanceLevel = getPerformanceLevel(overallScore || 200);

  // Prepare chart data
  const hasSubjectData = Object.keys(results.subjectScores).length > 0;
  const barChartData = {
    labels: hasSubjectData ? Object.keys(results.subjectScores).map(key => SUBJECT_NAMES[key]) : ['No Data'],
    datasets: [{
      label: 'Standardized Score',
      data: hasSubjectData ? Object.values(results.subjectScores).map(score => score.standardizedScore) : [200],
      backgroundColor: hasSubjectData ? Object.keys(results.subjectScores).map(key => SUBJECT_COLORS[key]) : ['#e5e7eb'],
      borderColor: hasSubjectData ? Object.keys(results.subjectScores).map(key => SUBJECT_COLORS[key]) : ['#9ca3af'],
      borderWidth: 1
    }]
  };

  const barChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      title: {
        display: true,
        text: 'Subject Scores (Standardized)',
        font: {
          size: 16
        }
      }
    },
    scales: {
      y: {
        beginAtZero: false,
        min: 200,
        max: 800,
        ticks: {
          stepSize: 100
        }
      }
    }
  };

  const radarChartData = {
    labels: hasSubjectData ? Object.keys(results.subjectScores).map(key => SUBJECT_NAMES[key]) : ['No Data'],
    datasets: [{
      label: 'Skill Level (%)',
      data: hasSubjectData ? Object.values(results.subjectScores).map(score => score.percentageScore) : [0],
      backgroundColor: 'rgba(54, 162, 235, 0.2)',
      borderColor: 'rgba(54, 162, 235, 1)',
      borderWidth: 2,
      pointBackgroundColor: 'rgba(54, 162, 235, 1)',
      pointBorderColor: '#fff',
      pointHoverBackgroundColor: '#fff',
      pointHoverBorderColor: 'rgba(54, 162, 235, 1)'
    }]
  };

  const radarChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      title: {
        display: true,
        text: 'Skills Radar Chart',
        font: {
          size: 16
        }
      }
    },
    scales: {
      r: {
        beginAtZero: true,
        max: 100,
        ticks: {
          stepSize: 20
        }
      }
    }
  };

  const accuracyChartData = {
    labels: ['Correct', 'Incorrect'],
    datasets: [{
      data: [results.summary.correctAnswers, results.summary.totalQuestions - results.summary.correctAnswers],
      backgroundColor: ['#4CAF50', '#F44336'],
      borderWidth: 0
    }]
  };

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold mb-2">Test Results</h1>
            <p className="text-gray-600">
              {results.config.mode === 'practice' ? 'Practice Mode' : 'Test Mode'} • 
              Completed on {new Date(results.summary.completedAt).toLocaleString()}
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={downloadReport}>
              <Download className="w-4 h-4 mr-2" />
              Download Report
            </Button>
            <Button variant="outline">
              <Share2 className="w-4 h-4 mr-2" />
              Share
            </Button>
          </div>
        </div>

        {/* Overall Performance Card */}
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <Trophy className="w-12 h-12 mx-auto mb-2 text-yellow-500" />
                <h3 className="text-4xl font-bold">{overallScore || 200}</h3>
                <p className="text-sm text-gray-600">Overall Score</p>
                <Badge className={`mt-2 border ${performanceLevel.badgeClass}`}>
                  {performanceLevel.label}
                </Badge>
              </div>
              <div className="text-center">
                <Target className="w-12 h-12 mx-auto mb-2 text-green-500" />
                <h3 className="text-4xl font-bold">{results.summary.accuracy}%</h3>
                <p className="text-sm text-gray-600">Accuracy</p>
                <p className="text-xs mt-1">
                  {results.summary.correctAnswers}/{results.summary.totalQuestions} Correct
                </p>
              </div>
              <div className="text-center">
                <Clock className="w-12 h-12 mx-auto mb-2 text-blue-500" />
                <h3 className="text-4xl font-bold">{formatTime(results.summary.totalTime)}</h3>
                <p className="text-sm text-gray-600">Total Time</p>
                <p className="text-xs mt-1">
                  Avg: {formatTime(results.summary.avgTimePerQuestion)}/question
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="subjects">By Subject</TabsTrigger>
          <TabsTrigger value="details">Question Details</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {!hasSubjectData ? (
            <Card>
              <CardContent className="p-8 text-center">
                <AlertCircle className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                <h3 className="text-xl font-semibold mb-2">No Questions Answered</h3>
                <p className="text-gray-600">
                  You ended the test without answering any questions.
                  Try starting a new test to see your performance analysis.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Bar Chart */}
              <Card>
                <CardContent className="p-6">
                  <div style={{ height: '400px' }}>
                    <Bar data={barChartData} options={barChartOptions} />
                  </div>
                </CardContent>
              </Card>

              {/* Radar Chart */}
              <Card>
                <CardContent className="p-6">
                  <div style={{ height: '400px' }}>
                    <Radar data={radarChartData} options={radarChartOptions} />
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Strengths and Weaknesses */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5 text-green-500" />
                  Strengths
                </CardTitle>
              </CardHeader>
              <CardContent>
                {results.strengths.length > 0 ? (
                  <ul className="space-y-2">
                    {results.strengths.map((strength, idx) => (
                      <li key={idx} className="flex items-center gap-2">
                        <CheckCircle2 className="w-4 h-4 text-green-500" />
                        <span>{SUBJECT_NAMES[strength] || strength}</span>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-gray-500">No standout strengths identified</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingDown className="w-5 h-5 text-red-500" />
                  Areas for Improvement
                </CardTitle>
              </CardHeader>
              <CardContent>
                {results.weaknesses.length > 0 ? (
                  <ul className="space-y-2">
                    {results.weaknesses.map((weakness, idx) => (
                      <li key={idx} className="flex items-center gap-2">
                        <XCircle className="w-4 h-4 text-red-500" />
                        <span>{SUBJECT_NAMES[weakness] || weakness}</span>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-gray-500">Great job! No major weaknesses found</p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* By Subject Tab */}
        <TabsContent value="subjects" className="space-y-4">
          {!hasSubjectData ? (
            <Card>
              <CardContent className="p-8 text-center">
                <AlertCircle className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                <h3 className="text-xl font-semibold mb-2">No Subject Data Available</h3>
                <p className="text-gray-600">
                  No questions were answered, so there's no subject-specific performance data to display.
                </p>
              </CardContent>
            </Card>
          ) : (
            Object.entries(results.subjectScores).map(([subject, scores]) => {
            const subjectPerf = getPerformanceLevel(scores.standardizedScore);
            
            return (
              <Card key={subject}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <div 
                        className="w-4 h-4 rounded" 
                        style={{ backgroundColor: SUBJECT_COLORS[subject] }}
                      />
                      {SUBJECT_NAMES[subject]}
                    </CardTitle>
                    <Badge className={`border ${subjectPerf.badgeClass}`}>
                      {scores.standardizedScore} - {subjectPerf.label}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Questions</p>
                      <p className="text-lg font-semibold">{scores.questionsAttempted}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Correct</p>
                      <p className="text-lg font-semibold">{scores.correctAnswers}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Accuracy</p>
                      <p className="text-lg font-semibold">{scores.accuracy}%</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Avg Difficulty</p>
                      <p className="text-lg font-semibold">Level {scores.avgDifficulty.toFixed(1)}</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm text-gray-600">Skill Level</span>
                      <span className="text-sm font-medium">{scores.percentageScore}%</span>
                    </div>
                    <Progress value={scores.percentageScore} className="h-2" />
                  </div>
                </CardContent>
              </Card>
            );
          })
          )}
        </TabsContent>

        {/* Question Details Tab */}
        <TabsContent value="details">
          <Card>
            <CardHeader>
              <CardTitle>Question-by-Question Breakdown</CardTitle>
              <CardDescription>
                Detailed performance on each question
              </CardDescription>
            </CardHeader>
            <CardContent>
              {results.detailedResponses.length === 0 ? (
                <div className="text-center py-8">
                  <AlertCircle className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-xl font-semibold mb-2">No Questions Answered</h3>
                  <p className="text-gray-600">
                    No questions were answered during this session.
                  </p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">#</th>
                        <th className="text-left p-2">Subject</th>
                        <th className="text-left p-2">Difficulty</th>
                        <th className="text-left p-2">Result</th>
                        <th className="text-left p-2">Time</th>
                      </tr>
                    </thead>
                    <tbody>
                      {results.detailedResponses.map((response, idx) => (
                      <tr key={idx} className="border-b hover:bg-gray-50">
                        <td className="p-2">{idx + 1}</td>
                        <td className="p-2">
                          <div className="flex items-center gap-2">
                            <div 
                              className="w-3 h-3 rounded" 
                              style={{ backgroundColor: SUBJECT_COLORS[response.subject] }}
                            />
                            {SUBJECT_NAMES[response.subject]}
                          </div>
                        </td>
                        <td className="p-2">Level {response.difficulty}</td>
                        <td className="p-2">
                          {response.correct ? (
                            <CheckCircle2 className="w-5 h-5 text-green-500" />
                          ) : (
                            <XCircle className="w-5 h-5 text-red-500" />
                          )}
                        </td>
                        <td className="p-2">{formatTime(response.timeSpent)}</td>
                      </tr>
                    ))}
                    </tbody>
                  </table>
                </div>
              )}

              {/* Accuracy Chart - only show if there are responses */}
              {results.detailedResponses.length > 0 && (
                <div className="mt-6 max-w-xs mx-auto">
                  <h4 className="text-center font-semibold mb-4">Overall Accuracy</h4>
                  <Doughnut
                    data={accuracyChartData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: true,
                      plugins: {
                        legend: {
                          position: 'bottom'
                        }
                      }
                    }}
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Insights Tab */}
        <TabsContent value="insights">
          <div className="space-y-6">
            {/* Recommendations */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="w-5 h-5" />
                  Personalized Recommendations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  {results.recommendations.map((rec, idx) => (
                    <li key={idx} className="flex items-start gap-2">
                      <AlertCircle className="w-5 h-5 text-blue-500 mt-0.5" />
                      <span>{rec}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            {/* Ability Progress */}
            <Card>
              <CardHeader>
                <CardTitle>Ability Development</CardTitle>
                <CardDescription>
                  How your ability estimates changed during the test
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.keys(SUBJECT_NAMES).map((subject, idx) => {
                    const initial = results.ability.initial[idx];
                    const final = results.ability.final[idx];
                    const change = results.ability.changes[idx];
                    const improved = change > 0;

                    return (
                      <div key={subject} className="flex items-center justify-between">
                        <span className="font-medium">{SUBJECT_NAMES[subject]}</span>
                        <div className="flex items-center gap-4">
                          <span className="text-sm text-gray-600">
                            {initial.toFixed(2)} → {final.toFixed(2)}
                          </span>
                          <Badge variant={improved ? "default" : "secondary"}>
                            {improved ? '+' : ''}{change.toFixed(2)}
                          </Badge>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Next Steps */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Next Steps
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button 
                    className="w-full" 
                    onClick={() => router.push('/test?mode=practice')}
                  >
                    Start Practice Mode
                  </Button>
                  <Button 
                    className="w-full" 
                    variant="outline"
                    onClick={() => router.push('/')}
                  >
                    Return to Home
                  </Button>
                </div>
                
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Study Tip</AlertTitle>
                  <AlertDescription>
                    {results.weaknesses.length > 0 
                      ? `Focus on ${results.weaknesses.slice(0, 2).map(w => SUBJECT_NAMES[w]).join(' and ')} to improve your overall performance.`
                      : 'Keep up the great work! Challenge yourself with higher difficulty problems.'}
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Main component with Suspense boundary
export default function ResultsPage() {
  return (
    <Suspense fallback={
      <div className="container mx-auto p-6 flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="w-12 h-12 animate-spin mx-auto mb-4" />
          <p className="text-lg">Loading results...</p>
        </div>
      </div>
    }>
      <ResultsContent />
    </Suspense>
  );
}