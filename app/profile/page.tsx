// app/profile/page.tsx
'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/auth-context'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  User, 
  Calendar, 
  TrendingUp, 
  BookOpen, 
  Clock,
  Trophy,
  Target,
  BarChart3,
  ArrowLeft
} from 'lucide-react'
import { useRouter } from 'next/navigation'

// Types
interface TestResponse {
  itemId: string;
  subject: string;
  difficulty: number;
  correct: boolean;
  userAnswer: string;
  timestamp: string;
  timeSpent: number;
}

interface TestSession {
  sessionId: string;
  userId?: string;
  mode: 'test' | 'practice';
  maxItems: number;
  targetSubjects: string[] | undefined;
  showSolution: boolean;
  timeLimit: number | undefined;
  startTime: string;
  currentAbility: number[];
  answeredItems: string[];
  responses: TestResponse[];
  recentPerformance: number[];
  subjectQuestionCount: Record<string, number>;
  status: 'active' | 'completed' | 'abandoned';
  completedAt?: string;
}

export default function ProfilePage() {
  const { user, loading } = useAuth()
  const [sessions, setSessions] = useState<TestSession[]>([])
  const [loadingSessions, setLoadingSessions] = useState(false)
  const router = useRouter()

  useEffect(() => {
    if (user && !loading) {
      loadUserSessions()
    }
  }, [user, loading])

  // Don't show access required immediately, wait for auth to initialize
  const shouldShowAccessRequired = !loading && !user

  const loadUserSessions = async () => {
    if (!user) return

    setLoadingSessions(true)
    try {
      // Add timeout to prevent hanging
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 10000) // 10 second timeout

      // Add cache-busting parameter to ensure fresh data
      const timestamp = Date.now()
      const response = await fetch(`/api/user/sessions?userId=${user.id}&t=${timestamp}`, {
        signal: controller.signal,
        cache: 'no-cache' // Ensure we get fresh data
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const data = await response.json()
      console.log('Loaded user sessions:', data.sessions?.length || 0, 'sessions')
      setSessions(data.sessions || [])
    } catch (error) {
      console.error('Error loading user sessions:', error)
      setSessions([]) // Set empty array on error
    } finally {
      setLoadingSessions(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-pulse flex space-x-4">
            <div className="rounded-full bg-slate-200 h-12 w-12"></div>
            <div className="flex-1 space-y-2 py-1">
              <div className="h-4 bg-slate-200 rounded w-3/4"></div>
              <div className="space-y-2">
                <div className="h-4 bg-slate-200 rounded"></div>
                <div className="h-4 bg-slate-200 rounded w-5/6"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (shouldShowAccessRequired) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Access Required</CardTitle>
            <CardDescription>
              You need to sign in to view your profile
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => router.push('/')} className="w-full">
              Go to Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  const completedSessions = sessions.filter(s => s.status === 'completed')
  const totalQuestions = completedSessions.reduce((sum, s) => sum + s.responses.length, 0)
  const correctAnswers = completedSessions.reduce((sum, s) => 
    sum + s.responses.filter(r => r.correct).length, 0)
  const averageAccuracy = totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatDuration = (startTime: string, completedAt?: string) => {
    const start = new Date(startTime)
    const end = completedAt ? new Date(completedAt) : new Date()
    const duration = Math.round((end.getTime() - start.getTime()) / 60000) // minutes
    return `${duration} min`
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-secondary/20">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button variant="ghost" size="sm" onClick={() => router.push('/')}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Profile Information */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="w-5 h-5" />
                  Profile
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  {user.user_metadata?.avatar_url && (
                    <img
                      src={user.user_metadata.avatar_url}
                      alt={user.user_metadata?.full_name || 'Profile'}
                      className="w-16 h-16 rounded-full mx-auto mb-4"
                    />
                  )}
                  <h3 className="font-semibold text-lg">
                    {user.user_metadata?.full_name || 'Anonymous User'}
                  </h3>
                  <p className="text-muted-foreground text-sm">{user.email}</p>
                </div>

                <div className="border-t pt-4">
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold text-primary">
                        {completedSessions.length}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Tests Completed
                      </div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-primary">
                        {averageAccuracy}%
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Average Score
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Statistics */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  Statistics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Questions Answered</span>
                    <span>{totalQuestions}</span>
                  </div>
                  <Progress value={Math.min(totalQuestions / 10, 100)} />
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Correct Answers</span>
                    <span>{correctAnswers}</span>
                  </div>
                  <Progress value={totalQuestions > 0 ? (correctAnswers / totalQuestions) * 100 : 0} />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Active Sessions</span>
                    <span>{sessions.filter(s => s.status === 'active').length}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Sessions */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Clock className="w-5 h-5" />
                      Recent Test Sessions
                    </CardTitle>
                    <CardDescription>
                      Your latest test and practice sessions
                    </CardDescription>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={loadUserSessions}
                    disabled={loadingSessions}
                  >
                    {loadingSessions ? 'Refreshing...' : 'Refresh'}
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {loadingSessions ? (
                  <div className="space-y-4">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="animate-pulse">
                        <div className="h-4 bg-slate-200 rounded w-3/4 mb-2"></div>
                        <div className="h-4 bg-slate-200 rounded w-1/2"></div>
                      </div>
                    ))}
                  </div>
                ) : sessions.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <BookOpen className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>No test sessions yet</p>
                    <p className="text-sm">Start your first test to see your progress here</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {sessions.slice(0, 10).map((session) => (
                      <div key={session.sessionId} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <Badge variant={session.mode === 'test' ? 'default' : 'secondary'}>
                              {session.mode === 'test' ? 'Formal Test' : 'Practice'}
                            </Badge>
                            <Badge 
                              variant={
                                session.status === 'completed' ? 'default' : 
                                session.status === 'active' ? 'secondary' : 'destructive'
                              }
                            >
                              {session.status}
                            </Badge>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {formatDate(session.startTime)}
                          </div>
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <div className="text-muted-foreground">Questions</div>
                            <div className="font-semibold">
                              {session.responses.length}/{session.maxItems}
                            </div>
                          </div>
                          <div>
                            <div className="text-muted-foreground">Accuracy</div>
                            <div className="font-semibold">
                              {session.responses.length > 0 
                                ? Math.round((session.responses.filter(r => r.correct).length / session.responses.length) * 100)
                                : 0}%
                            </div>
                          </div>
                          <div>
                            <div className="text-muted-foreground">Duration</div>
                            <div className="font-semibold">
                              {formatDuration(session.startTime, session.completedAt)}
                            </div>
                          </div>
                          <div>
                            <div className="text-muted-foreground">Score</div>
                            <div className="font-semibold flex items-center gap-1">
                              {session.responses.filter(r => r.correct).length}
                              <Trophy className="w-4 h-4 text-yellow-500" />
                            </div>
                          </div>
                        </div>

                        {session.status === 'active' && (
                          <div className="mt-3 pt-3 border-t">
                            <Button size="sm" onClick={() => router.push(`/test?sessionId=${session.sessionId}&mode=${session.mode}&question=${session.responses.length + 1}`)}>
                              Resume Session
                            </Button>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}