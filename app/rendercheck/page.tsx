'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Suspense } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  ChevronLeft,
  ChevronRight,
  Eye,
  EyeOff,
  BookOpen,
  AlertCircle,
  Home,
  RotateCcw,
  Info
} from 'lucide-react';
import 'katex/dist/katex.min.css';

// Import shared LaTeX rendering components
import { MixedContent } from '@/components/latex-renderer';

// Import shared problem loading logic
import {
  ProblemResponse
} from '@/lib/problem-loader';















// Use shared ProblemResponse interface
type Problem = ProblemResponse;

interface ProblemData {
  index: number;
  total: number;
  problem: Problem;
  filename: string;
  dataset: string;
  navigation: {
    hasPrevious: boolean;
    hasNext: boolean;
    previousIndex: number | null;
    nextIndex: number | null;
  };
}

interface MultiProblemData {
  startIndex: number;
  endIndex: number;
  perpage: number;
  total: number;
  problems: Array<{
    index: number;
    problem: Problem;
    filename: string;
    dataset: string;
  }>;
  navigation: {
    hasPrevious: boolean;
    hasNext: boolean;
    previousStartIndex: number | null;
    nextStartIndex: number | null;
  };
}

// Main content component
function RenderCheckContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [problemData, setProblemData] = useState<ProblemData | null>(null);
  const [multiProblemData, setMultiProblemData] = useState<MultiProblemData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showSolution, setShowSolution] = useState(true);

  const currentIndex = parseInt(searchParams.get('question') || '1');
  const perpage = parseInt(searchParams.get('perpage') || '20');
  const isMultiMode = perpage > 1;

  // Load problem data - using shared API endpoint but keeping rendercheck-specific multi-problem logic
  const loadProblem = useCallback(async (index: number, perpageParam?: number) => {
    setLoading(true);
    setError(null);
    setProblemData(null);
    setMultiProblemData(null);

    try {
      const requestBody = perpageParam && perpageParam > 1
        ? { index, perpage: perpageParam }
        : { index };

      // Use the same API endpoint as before - rendercheck has special multi-problem functionality
      const response = await fetch('/api/rendercheck/list', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        if (response.status === 404) {
          setError(`Problem ${index} not found. Please check the question number.`);
        } else {
          throw new Error('Failed to load problem');
        }
        return;
      }

      const data = await response.json();

      if (perpageParam && perpageParam > 1) {
        setMultiProblemData(data);
      } else {
        setProblemData(data);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load problem');
    } finally {
      setLoading(false);
    }
  }, []);

  // Navigation functions
  const navigateToIndex = useCallback((index: number, perpageParam?: number) => {
    const url = perpageParam && perpageParam > 1
      ? `/rendercheck?question=${index}&perpage=${perpageParam}`
      : `/rendercheck?question=${index}`;
    router.push(url);
  }, [router]);

  const handlePrevious = useCallback(() => {
    if (isMultiMode && multiProblemData?.navigation.hasPrevious && multiProblemData.navigation.previousStartIndex) {
      navigateToIndex(multiProblemData.navigation.previousStartIndex, perpage);
    } else if (!isMultiMode && problemData?.navigation.hasPrevious && problemData.navigation.previousIndex) {
      navigateToIndex(problemData.navigation.previousIndex);
    }
  }, [isMultiMode, multiProblemData, problemData, navigateToIndex, perpage]);

  const handleNext = useCallback(() => {
    if (isMultiMode && multiProblemData?.navigation.hasNext && multiProblemData.navigation.nextStartIndex) {
      navigateToIndex(multiProblemData.navigation.nextStartIndex, perpage);
    } else if (!isMultiMode && problemData?.navigation.hasNext && problemData.navigation.nextIndex) {
      navigateToIndex(problemData.navigation.nextIndex);
    }
  }, [isMultiMode, multiProblemData, problemData, navigateToIndex, perpage]);

  // Keyboard event handlers
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return; // Don't handle if user is typing in an input
      }

      if (event.code === 'Space') {
        event.preventDefault();
        if (event.shiftKey) {
          handlePrevious();
        } else {
          handleNext();
        }
      } else if (event.code === 'ArrowLeft') {
        event.preventDefault();
        handlePrevious();
      } else if (event.code === 'ArrowRight') {
        event.preventDefault();
        handleNext();
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleNext, handlePrevious]);

  // Load problem when index or perpage changes
  useEffect(() => {
    if (currentIndex >= 1) {
      loadProblem(currentIndex, perpage);
    } else {
      setError('Invalid question number');
      setLoading(false);
    }
  }, [currentIndex, perpage, loadProblem]);

  if (loading) {
    return (
      <div className="container mx-auto p-6 max-w-6xl">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <BookOpen className="w-12 h-12 animate-pulse mx-auto mb-4" />
            <p className="text-lg">Loading problem {currentIndex}...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6 max-w-6xl">
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <div className="flex gap-2">
          <Button onClick={() => router.push('/rendercheck?question=1')} variant="outline">
            <Home className="w-4 h-4 mr-2" />
            Go to First Problem
          </Button>
          <Button onClick={() => router.push('/test')} variant="outline">
            <RotateCcw className="w-4 h-4 mr-2" />
            Back to Test
          </Button>
        </div>
      </div>
    );
  }

  if (!problemData && !multiProblemData) {
    return null;
  }

  // Multi-problem mode
  if (isMultiMode && multiProblemData) {
    return (
      <div className="container mx-auto p-6 max-w-6xl">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-bold flex items-center gap-2">
                <BookOpen className="w-6 h-6" />
                Render Check Mode - Multi View
              </h1>
              <p className="text-gray-600">
                Problems {multiProblemData.startIndex}-{multiProblemData.endIndex} of {multiProblemData.total} ({multiProblemData.perpage} per page)
              </p>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSolution(!showSolution)}
              >
                {showSolution ? <EyeOff className="w-4 h-4 mr-2" /> : <Eye className="w-4 h-4 mr-2" />}
                {showSolution ? 'Hide' : 'Show'} All Solutions
              </Button>
            </div>
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-between">
            <Button
              onClick={handlePrevious}
              disabled={!multiProblemData.navigation.hasPrevious}
              variant="outline"
            >
              <ChevronLeft className="w-4 h-4 mr-2" />
              Previous {multiProblemData.perpage}
            </Button>

            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">
                Page {Math.ceil(multiProblemData.startIndex / multiProblemData.perpage)} of {Math.ceil(multiProblemData.total / multiProblemData.perpage)}
              </span>
            </div>

            <Button
              onClick={handleNext}
              disabled={!multiProblemData.navigation.hasNext}
              variant="outline"
            >
              Next {multiProblemData.perpage}
              <ChevronRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </div>

        {/* Problems Grid */}
        <div className="space-y-8">
          {multiProblemData.problems.map((problemItem) => (
            <Card key={problemItem.index} className="p-6">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">Problem {problemItem.index}</CardTitle>
                    <p className="text-sm text-gray-500 mt-1">
                      File: {problemItem.dataset}/{problemItem.problem.subject}/{problemItem.filename}
                    </p>
                    <div className="flex items-center gap-2 mt-2">
                      <Badge variant="outline">{problemItem.problem.subject}</Badge>
                      <Badge variant="secondary">{problemItem.problem.level}</Badge>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Problem Statement */}
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Problem:</h3>
                    <div className="p-4 bg-blue-50 border border-blue-200 rounded">
                      <MixedContent content={problemItem.problem.problem} />
                    </div>
                  </div>

                  {/* Solution */}
                  {showSolution && (
                    <div>
                      <h3 className="text-lg font-semibold mb-2">Solution:</h3>
                      <div className="p-4 bg-gray-50 border border-gray-200 rounded">
                        <MixedContent content={problemItem.problem.solution || ''} />
                      </div>
                    </div>
                  )}

                  {/* Final Answer */}
                  {showSolution && problemItem.problem.correctAnswer && (
                    <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded">
                      <p className="text-sm text-green-700 mb-1">Final Answer:</p>
                      <div className="space-y-3">
                        {/* Raw expression */}
                        <div>
                          <p className="text-xs text-gray-600 mb-1">Raw expression:</p>
                          <span className="inline-block px-3 py-1 bg-blue-100 border-2 border-blue-300 rounded font-mono text-sm">
                            {Array.isArray(problemItem.problem.correctAnswer) ? problemItem.problem.correctAnswer.join(', ') : problemItem.problem.correctAnswer}
                          </span>
                        </div>
                        {/* Rendered formula */}
                        <div>
                          <p className="text-xs text-gray-600 mb-1">Rendered:</p>
                          <div className="text-xl">
                            <MixedContent 
                              content={`$\\boxed{${Array.isArray(problemItem.problem.correctAnswer) ? problemItem.problem.correctAnswer.join(', ') : problemItem.problem.correctAnswer}}$`} 
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Bottom Navigation for Multi-problem mode */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <Button
              onClick={handlePrevious}
              disabled={!multiProblemData.navigation.hasPrevious}
              variant="outline"
            >
              <ChevronLeft className="w-4 h-4 mr-2" />
              Previous {multiProblemData.perpage}
            </Button>

            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">
                Page {Math.ceil(multiProblemData.startIndex / multiProblemData.perpage)} of {Math.ceil(multiProblemData.total / multiProblemData.perpage)}
              </span>
            </div>

            <Button
              onClick={handleNext}
              disabled={!multiProblemData.navigation.hasNext}
              variant="outline"
            >
              Next {multiProblemData.perpage}
              <ChevronRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Single problem mode (original)
  if (!problemData) {
    return null;
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <BookOpen className="w-6 h-6" />
              Render Check Mode
            </h1>
            <p className="text-gray-600">
              Problem {problemData.index} of {problemData.total}
            </p>
            <p className="text-sm text-gray-500">
              File: {problemData.dataset}/{problemData.problem.subject}/{problemData.filename}
            </p>
          </div>

          <div className="flex items-center gap-2">
            <Badge variant="outline">{problemData.problem.subject}</Badge>
            <Badge variant="secondary">{problemData.problem.level}</Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSolution(!showSolution)}
            >
              {showSolution ? <EyeOff className="w-4 h-4 mr-2" /> : <Eye className="w-4 h-4 mr-2" />}
              {showSolution ? 'Hide' : 'Show'} Solution
            </Button>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex items-center justify-between">
          <Button
            onClick={handlePrevious}
            disabled={!problemData.navigation.hasPrevious}
            variant="outline"
          >
            <ChevronLeft className="w-4 h-4 mr-2" />
            Previous
          </Button>

          <div className="text-sm text-gray-500 text-center">
            <p>Use <kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs">←</kbd> for previous, <kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs">→</kbd> for next</p>
            <p>Or <kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs">Space</kbd> for next, <kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs">Shift+Space</kbd> for previous</p>
          </div>

          <Button
            onClick={handleNext}
            disabled={!problemData.navigation.hasNext}
            variant="outline"
          >
            Next
            <ChevronRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>

      {/* Problem Content */}
      <div className="grid gap-6">
        {/* Problem */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="w-5 h-5" />
              Problem
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-lg leading-relaxed">
              <MixedContent content={problemData.problem.problem} />
            </div>
          </CardContent>
        </Card>

        {/* Solution */}
        {showSolution && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="w-5 h-5" />
                Solution
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-base leading-relaxed">
                <MixedContent content={problemData.problem.solution || ''} />
              </div>
              {problemData.problem.correctAnswer && (
                <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded">
                  <p className="text-sm text-green-700 mb-1">Final Answer:</p>
                  <div className="space-y-3">
                    {/* Raw expression */}
                    <div>
                      <p className="text-xs text-gray-600 mb-1">Raw expression:</p>
                      <span className="inline-block px-3 py-1 bg-blue-100 border-2 border-blue-300 rounded font-mono text-sm">
                        {Array.isArray(problemData.problem.correctAnswer) ? problemData.problem.correctAnswer.join(', ') : problemData.problem.correctAnswer}
                      </span>
                    </div>
                    {/* Rendered formula */}
                    <div>
                      <p className="text-xs text-gray-600 mb-1">Rendered:</p>
                      <div className="text-xl">
                        <MixedContent 
                          content={`$\\boxed{${Array.isArray(problemData.problem.correctAnswer) ? problemData.problem.correctAnswer.join(', ') : problemData.problem.correctAnswer}}$`} 
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Bottom Navigation for Single problem mode */}
      <div className="mt-8 pt-6 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <Button
            onClick={handlePrevious}
            disabled={!problemData.navigation.hasPrevious}
            variant="outline"
          >
            <ChevronLeft className="w-4 h-4 mr-2" />
            Previous
          </Button>

          <div className="text-sm text-gray-500 text-center">
            <p>Use <kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs">←</kbd> for previous, <kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs">→</kbd> for next</p>
            <p>Or <kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs">Space</kbd> for next, <kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs">Shift+Space</kbd> for previous</p>
          </div>

          <Button
            onClick={handleNext}
            disabled={!problemData.navigation.hasNext}
            variant="outline"
          >
            Next
            <ChevronRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>
    </div>
  );
}

// Main component with Suspense boundary
export default function RenderCheckPage() {
  return (
    <Suspense fallback={
      <div className="container mx-auto p-6 flex items-center justify-center min-h-screen">
        <div className="text-center">
          <BookOpen className="w-12 h-12 animate-pulse mx-auto mb-4" />
          <p className="text-lg">Loading render check...</p>
        </div>
      </div>
    }>
      <RenderCheckContent />
    </Suspense>
  );
}
