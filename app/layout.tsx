// math-adaptive-test/app/layout.tsx

import type { Metadata } from "next";
import "./globals.css";
import { Toaster } from "@/components/ui/toaster";
import { ThemeProvider } from "@/components/theme-provider";
import { AuthProvider } from "@/lib/auth-context";
import { Analytics } from '@vercel/analytics/react';

// Initialize global error handling
import '@/lib/error-handler';

export const metadata: Metadata = {
  title: "Math Adaptive Test - Personalized Mathematics Assessment",
  description: "An adaptive mathematics testing platform that adjusts difficulty based on your performance. Practice and assess your skills in algebra, geometry, number theory, and more.",
  keywords: [
    "math test",
    "adaptive testing",
    "mathematics assessment",
    "algebra practice",
    "geometry problems",
    "number theory",
    "math skills",
    "online math test",
    "personalized learning",
    "MATH dataset"
  ],
  authors: [{ name: "Math Adaptive Test Team" }],
  creator: "Math Adaptive Test",
  publisher: "Math Adaptive Test",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    title: "Math Adaptive Test - Personalized Mathematics Assessment",
    description: "Improve your math skills with our adaptive testing platform",
    url: "https://math-adaptive-test.com",
    siteName: "Math Adaptive Test",
    images: [
      {
        url: "/og-image.png",
        width: 1200,
        height: 630,
        alt: "Math Adaptive Test",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Math Adaptive Test",
    description: "Improve your math skills with our adaptive testing platform",
    images: ["/og-image.png"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  icons: {
    icon: [
      { url: "/favicon.ico" },
      { url: "/favicon-16x16.png", sizes: "16x16", type: "image/png" },
      { url: "/favicon-32x32.png", sizes: "32x32", type: "image/png" },
    ],
    apple: [
      { url: "/apple-touch-icon.png" },
    ],
  },
  manifest: "/site.webmanifest",
  alternates: {
    canonical: "https://math-adaptive-test.com",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Viewport for mobile responsiveness */}
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
        
        {/* Theme color for mobile browsers */}
        <meta name="theme-color" content="#ffffff" media="(prefers-color-scheme: light)" />
        <meta name="theme-color" content="#0a0a0a" media="(prefers-color-scheme: dark)" />
        
        {/* Preconnect to external domains for better performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        
        {/* Load Google Fonts directly */}
        <link
          href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&display=swap"
          rel="stylesheet"
        />
        
        {/* KaTeX CSS for LaTeX rendering */}
        <link
          rel="stylesheet"
          href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css"
          integrity="sha384-n8MVd4RsNIU0tAv4ct0nTaAbDJwPJzDEaqSD1odI+WdtXRGWt2kTvGFasHpSy3SV"
          crossOrigin="anonymous"
        />
        
        {/* Preload critical fonts */}
        <link
          rel="preload"
          href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/fonts/KaTeX_Main-Regular.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />
      </head>
      <body
        className="font-sans antialiased min-h-screen bg-background"
        style={{ fontFamily: 'Inter, system-ui, -apple-system, sans-serif' }}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          <AuthProvider>
            {/* Skip to main content for accessibility */}
            <a href="#main-content" className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary text-primary-foreground px-4 py-2 rounded">
              Skip to main content
            </a>
            
            {/* Main application content */}
            <main id="main-content" className="relative">
              {children}
            </main>
            
            {/* Toast notifications */}
            <Toaster />
            
            {/* No-script fallback */}
            <noscript>
              <div className="fixed inset-0 flex items-center justify-center bg-background">
                <div className="text-center p-8">
                  <h1 className="text-2xl font-bold mb-4">JavaScript Required</h1>
                  <p className="text-muted-foreground">
                    This application requires JavaScript to function properly. 
                    Please enable JavaScript in your browser settings.
                  </p>
                </div>
              </div>
            </noscript>
          </AuthProvider>
        </ThemeProvider>
        
        {/* Analytics (only in production) */}
        {process.env.NODE_ENV === 'production' && <Analytics />}
      </body>
    </html>
  );
}
