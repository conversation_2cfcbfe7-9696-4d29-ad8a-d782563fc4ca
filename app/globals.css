@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  --font-mono: 'JetBrains Mono', 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', monospace;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    border-color: theme('colors.gray.200');
    outline-color: theme('colors.gray.500/50');
  }
  body {
    background-color: var(--background);
    color: var(--foreground);
  }
}

/* Math Adaptive Test Custom Styles */
@layer components {
  /* Math Problem Display */
  .math-problem {
    @apply text-lg leading-relaxed;
  }

  .math-problem .katex {
    @apply text-xl;
  }

  .math-problem .katex-display {
    @apply my-4 overflow-x-auto;
  }

  /* Test Progress Indicator */
  .test-progress-bar {
    @apply relative overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700;
  }

  .test-progress-fill {
    @apply h-full bg-blue-600 dark:bg-blue-500 transition-all duration-300 ease-out;
  }

  /* Chart Container */
  .chart-container {
    @apply relative w-full;
    min-height: 300px;
  }

  /* Results Cards */
  .result-card {
    @apply bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-700 shadow-lg;
  }

  /* Animations */
  .fade-in {
    animation: fadeIn 0.3s ease-in;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .pulse-success {
    animation: pulseSuccess 0.6s ease-out;
  }

  @keyframes pulseSuccess {
    0% {
      box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
    }
  }

  .pulse-error {
    animation: pulseError 0.6s ease-out;
  }

  @keyframes pulseError {
    0% {
      box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    }
  }
  /* 统一LaTeX分数显示 */
  .katex .frac {
    font-size: 0.9em !important;
  }
  
  /* 控制boxed环境内的字体大小 */
  .katex .boxed {
    font-size: 0.95em !important;
    padding: 0.2em 0.4em !important;
  }
  
  /* 确保内联数学与文本对齐 */
  .katex {
    font-size: 1em !important;
    vertical-align: baseline !important;
  }
  
  /* 调整显示数学的间距 */
  .katex-display {
    margin: 0.8em 0 !important;
    text-align: center !important;
  }
  
  /* 响应式调整 */
  @media (max-width: 640px) {
    .katex {
      font-size: 0.95em !important;
    }
    
    .katex .boxed {
      font-size: 0.9em !important;
      padding: 0.15em 0.3em !important;
    }
  }
}

/* Utility Classes */
@layer utilities {
  /* Scrollbar Styling */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: theme('colors.gray.300') theme('colors.white');
  }

  .scrollbar-thin::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    @apply bg-white dark:bg-gray-900;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-500 dark:bg-gray-400;
  }

  /* LaTeX Math Overflow Handling */
  .math-overflow-auto {
    @apply overflow-x-auto pb-2;
  }

  .math-overflow-auto .katex-display {
    @apply min-w-0;
  }
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }

  .math-problem {
    color: black;
    page-break-inside: avoid;
  }
}

/* Chart.js Custom Styling */
.chart-container canvas {
  width: 100% !important;
  height: auto !important;
}

/* LaTeX Specific Overrides */
.katex {
  color: currentColor;
  background-color: transparent !important;
}

.katex-display {
  text-align: left !important;
  background-color: transparent !important;
}

/* Light mode LaTeX styling (default) */
:root .katex {
  color: var(--foreground);
}

/* Dark mode adjustments for LaTeX */
.dark .katex {
  color: var(--foreground);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .math-problem .katex {
    font-size: 1rem;
  }
  
  .math-answer-input {
    font-size: 1rem;
  }
}
