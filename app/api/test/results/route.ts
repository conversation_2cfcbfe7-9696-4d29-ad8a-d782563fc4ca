// math-adaptive-test/app/api/test/results/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { getSessionById } from '../session-store';

// Types
interface TestResults {
    sessionId: string;
    status: string;
    config: {
        mode: 'test' | 'practice';
        maxItems: number;
        targetSubjects: string[] | null;
        timeLimit: number | null;
    };
    summary: {
        totalQuestions: number;
        correctAnswers: number;
        accuracy: number;
        avgTimePerQuestion: number;
        totalTime: number;
        completedAt: string;
    };
    ability: {
        final: number[];
        initial: number[];
        changes: number[];
    };
    subjectScores: Record<string, {
        standardizedScore: number;
        percentageScore: number;
        questionsAttempted: number;
        correctAnswers: number;
        accuracy: number;
        avgDifficulty: number;
    }>;
    strengths: string[];
    weaknesses: string[];
    recommendations: string[];
    detailedResponses: Array<{
        itemId: string;
        subject: string;
        difficulty: number;
        correct: boolean;
        timeSpent: number;
        timestamp: string;
    }>;
}

// Subject mapping
const SUBJECT_NAMES: Record<string, string> = {
    'algebra': 'Algebra',
    'counting_and_probability': 'Counting & Probability',
    'geometry': 'Geometry',
    'intermediate_algebra': 'Intermediate Algebra',
    'number_theory': 'Number Theory',
    'prealgebra': 'Pre-algebra'
};

// API Route Handler
export async function GET(request: NextRequest) {
    try {
        const { searchParams } = new URL(request.url);
        const sessionId = searchParams.get('sessionId');

        if (!sessionId) {
            return NextResponse.json(
                { error: 'Session ID is required' },
                { status: 400 }
            );
        }

        // Get session
        const session = await getSessionById(sessionId);
        if (!session) {
            return NextResponse.json(
                { error: 'Session not found' },
                { status: 404 }
            );
        }

        // Calculate total time
        const startTime = new Date(session.startTime).getTime();
        const endTime = session.completedAt ?
            new Date(session.completedAt).getTime() :
            new Date().getTime();
        const totalTime = Math.floor((endTime - startTime) / 1000);

        // Calculate average time per question
        const avgTimePerQuestion = session.responses.length > 0 ?
            Math.floor(session.responses.reduce((sum, r) => sum + r.timeSpent, 0) / session.responses.length) :
            0;

        // Calculate subject scores
        const subjectScores: Record<string, any> = {};
        const subjectResponses: Record<string, any[]> = {};

        // Group responses by subject
        for (const response of session.responses) {
            if (!subjectResponses[response.subject]) {
                subjectResponses[response.subject] = [];
            }
            subjectResponses[response.subject].push(response);
        }

        // Calculate scores for each subject
        for (const subject in subjectResponses) {
            const responses = subjectResponses[subject];
            const questionsAttempted = responses.length;
            const correctAnswers = responses.filter(r => r.correct).length;
            const accuracy = questionsAttempted > 0 ?
                Math.round((correctAnswers / questionsAttempted) * 100) : 0;
            const avgDifficulty = responses.reduce((sum, r) => sum + r.difficulty, 0) / questionsAttempted;

            // Calculate standardized score - more realistic scoring
            // Base score starts at 200 (minimum), with accuracy being the primary factor
            let standardizedScore = 200; // Minimum score

            if (accuracy > 0) {
                // Accuracy contributes 0-600 points (200-800 range)
                const accuracyPoints = (accuracy / 100) * 600;

                // Difficulty bonus: harder questions give slight bonus when answered correctly
                const difficultyMultiplier = Math.max(0.8, Math.min(1.2, avgDifficulty / 3));

                standardizedScore = Math.round(200 + (accuracyPoints * difficultyMultiplier));
            }

            // Ensure score stays within reasonable bounds (200-800)
            standardizedScore = Math.max(200, Math.min(800, standardizedScore));

            // Calculate percentage score (same as accuracy, but with difficulty consideration)
            let percentageScore = accuracy;
            if (accuracy > 0 && avgDifficulty > 3) {
                // Slight bonus for harder questions
                percentageScore = Math.min(100, Math.round(accuracy * 1.1));
            }

            subjectScores[subject] = {
                standardizedScore,
                percentageScore,
                questionsAttempted,
                correctAnswers,
                accuracy,
                avgDifficulty
            };
        }

        // Identify strengths and weaknesses based on accuracy
        const subjectPerformance = Object.entries(subjectScores)
            .map(([subject, scores]) => ({
                subject,
                score: scores.standardizedScore,
                accuracy: scores.accuracy
            }))
            .sort((a, b) => b.score - a.score);

        // Strengths: subjects with >70% accuracy
        const strengths = subjectPerformance
            .filter(item => item.accuracy >= 70)
            .map(item => item.subject);

        // Weaknesses: subjects with <50% accuracy
        const weaknesses = subjectPerformance
            .filter(item => item.accuracy < 50)
            .map(item => item.subject);

        // Generate recommendations
        const recommendations: string[] = [];

        if (weaknesses.length > 0) {
            for (const subject of weaknesses) {
                recommendations.push(`Focus on improving your ${SUBJECT_NAMES[subject]} skills with targeted practice.`);
            }
        }

        if (strengths.length > 0) {
            recommendations.push(`Continue to build on your strengths in ${strengths.map(s => SUBJECT_NAMES[s]).join(', ')}.`);
        }

        if (session.responses.length > 0) {
            const avgTime = session.responses.reduce((sum, r) => sum + r.timeSpent, 0) / session.responses.length;
            if (avgTime > 120) {
                recommendations.push('Work on improving your speed while maintaining accuracy.');
            }
        }

        if (recommendations.length === 0) {
            recommendations.push('Continue practicing across all subjects to maintain balanced skills.');
        }

        // Prepare results
        const results: TestResults = {
            sessionId: session.sessionId,
            status: session.status,
            config: {
                mode: session.mode,
                maxItems: session.maxItems,
                targetSubjects: session.targetSubjects || null,
                timeLimit: session.timeLimit || null
            },
            summary: {
                totalQuestions: session.responses.length,
                correctAnswers: session.responses.filter(r => r.correct).length,
                accuracy: session.responses.length > 0 ?
                    Math.round((session.responses.filter(r => r.correct).length / session.responses.length) * 100) : 0,
                avgTimePerQuestion,
                totalTime,
                completedAt: session.completedAt || new Date().toISOString()
            },
            ability: {
                final: session.currentAbility,
                initial: [0, 0, 0, 0, 0, 0], // Assuming initial ability was all zeros
                changes: session.currentAbility.map(a => a - 0) // Calculate changes from initial
            },
            subjectScores,
            strengths,
            weaknesses,
            recommendations,
            detailedResponses: session.responses.map(r => ({
                itemId: r.itemId,
                subject: r.subject,
                difficulty: r.difficulty,
                correct: r.correct,
                timeSpent: r.timeSpent,
                timestamp: r.timestamp
            }))
        };

        return NextResponse.json(results);

    } catch (error) {
        console.error('Error in /api/test/results:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}