// Debug endpoint to check session store
import { NextRequest, NextResponse } from 'next/server';
import { getSessionById, setSession } from '../session-store';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const sessionId = searchParams.get('sessionId');

  if (sessionId) {
    const session = await getSessionById(sessionId);
    return NextResponse.json({
      sessionId,
      exists: !!session,
      session: session || null
    });
  }

  return NextResponse.json({
    message: 'Provide sessionId as query parameter'
  });
}

// POST endpoint to create a test session
export async function POST(request: NextRequest) {
  const testSessionId = 'test-session-123';
  const testSession = {
    sessionId: testSessionId,
    mode: 'practice' as const,
    maxItems: 5,
    targetSubjects: undefined,
    showSolution: true,
    timeLimit: undefined,
    startTime: new Date().toISOString(),
    currentAbility: [0, 0, 0, 0, 0, 0],
    answeredItems: [],
    responses: [],
    recentPerformance: [],
    subjectQuestionCount: {
      algebra: 0,
      counting_and_probability: 0,
      geometry: 0,
      intermediate_algebra: 0,
      number_theory: 0,
      prealgebra: 0
    },
    status: 'active' as const
  };

  await setSession(testSessionId, testSession);

  // Immediately try to retrieve it
  const retrieved = await getSessionById(testSessionId);

  return NextResponse.json({
    created: testSessionId,
    retrieved: !!retrieved,
    session: retrieved
  });
}