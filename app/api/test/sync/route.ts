// app/api/test/sync/route.ts
// Test endpoint to verify session synchronization

import { NextRequest, NextResponse } from 'next/server';
import { getSessionById, getUserSessions } from '../session-store';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');
    const userId = searchParams.get('userId');

    if (sessionId) {
      // Test single session retrieval
      const session = await getSessionById(sessionId);
      return NextResponse.json({
        type: 'single_session',
        sessionId,
        found: !!session,
        session: session ? {
          sessionId: session.sessionId,
          userId: session.userId,
          mode: session.mode,
          status: session.status,
          responses: session.responses.length,
          answeredItems: session.answeredItems.length,
          startTime: session.startTime,
          completedAt: session.completedAt
        } : null
      });
    }

    if (userId) {
      // Test user sessions retrieval
      const sessions = await getUserSessions(userId);
      return NextResponse.json({
        type: 'user_sessions',
        userId,
        count: sessions.length,
        sessions: sessions.map(s => ({
          sessionId: s.sessionId,
          mode: s.mode,
          status: s.status,
          responses: s.responses.length,
          startTime: s.startTime,
          completedAt: s.completedAt
        }))
      });
    }

    return NextResponse.json({
      error: 'Provide either sessionId or userId as query parameter'
    }, { status: 400 });

  } catch (error) {
    console.error('Error in sync test:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
