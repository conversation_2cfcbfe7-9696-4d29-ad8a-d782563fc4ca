// math-adaptive-test/app/api/test/submit/route.ts

import { NextRequest, NextResponse } from 'next/server';
// ✅ 关键改动: 从共享的 session-store 导入
import { getSessionById, setSession, TestResponse } from '../session-store';
import { PythonShell } from 'python-shell'; // ✅ 1. 导入 PythonShell
// 从 data-loader 导入所有需要的函数
import { getProblemById, loadProblems, initializeItemParams } from '@/lib/data-loader';

// ... (keep the rest of the file the same until the POST function)

// API Route Handler for submitting answers
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const { sessionId, itemId, userAnswer, timeSpent } = body;

    if (!sessionId || !itemId) {
      return NextResponse.json({ error: 'Session ID and Item ID are required' }, { status: 400 });
    }

    // ✅ 关键改动: 使用共享的 getSessionById 函数
    const session = await getSessionById(sessionId);
    if (!session) {
      // 这是你遇到错误的地方
      return NextResponse.json({ error: 'Session not found' }, { status: 404 });
    }

    const problem = await getProblemById(itemId);
    if (!problem) {
      return NextResponse.json({ error: 'Problem data not found' }, { status: 404 });
    }
    
    const correctAnswer = problem.correctAnswer || '';

    // 1. 答案检查 (improved error handling)
    let isCorrect = false;
    try {
      const answerCheckOptions = {
        mode: 'json' as const,
        pythonPath: process.env.PYTHON_PATH || 'python3',
        scriptPath: './python',
        args: [userAnswer || '', correctAnswer]
      };
      const shellResult = await PythonShell.run('answer_checker.py', answerCheckOptions);
      isCorrect = shellResult[0].correct;
    } catch (answerCheckError) {
      console.error('Answer checking failed, falling back to simple comparison:', answerCheckError);
      // Fallback to simple string comparison if Python fails
      const normalizedUserAnswer = (userAnswer || '').trim().toLowerCase();
      const normalizedCorrectAnswer = correctAnswer.trim().toLowerCase();
      isCorrect = normalizedUserAnswer === normalizedCorrectAnswer;
    }

    // 2. 准备调用Python MLE脚本所需的数据
    const currentResponse: TestResponse = {
      itemId,
      subject: problem.subject,
      difficulty: problem.difficulty,
      correct: isCorrect,
      userAnswer: userAnswer || '',
      timestamp: new Date().toISOString(),
      timeSpent: timeSpent || 0
    };
    
    // 包含本次回答的完整历史记录
    const updatedResponses = [...session.responses, currentResponse];

    // 加载所有题目并初始化参数，这是Python脚本需要的
    const allProblems = await loadProblems();
    const allItemParams = initializeItemParams(allProblems);
    const itemParamsObject = Object.fromEntries(allItemParams.entries());

    const abilityUpdateArgs = {
        current_ability: session.currentAbility,
        responses: updatedResponses,
        item_params: itemParamsObject,
    };

    // 3. 调用 ability_updater.py 脚本 - 使用stdin避免参数过长 (improved error handling)
    let newAbility = session.currentAbility;
    try {
      const abilityResult = await new Promise((resolve, reject) => {
        const pyshell = new PythonShell('ability_updater.py', {
          mode: 'json' as const,
          pythonPath: process.env.PYTHON_PATH || 'python3',
          scriptPath: './python',
        });

        let hasEnded = false;
        const results: any[] = [];

        // Handle connection errors
        pyshell.on('error', (err) => {
          console.error('Python shell error:', err);
          if (!hasEnded) {
            hasEnded = true;
            reject(err);
          }
        });

        pyshell.on('message', (message) => {
          results.push(message);
        });

        pyshell.on('close', (code) => {
          if (!hasEnded) {
            hasEnded = true;
            resolve(results);
          }
        });

        // Send data with error handling
        try {
          pyshell.send(abilityUpdateArgs);
          pyshell.end((err: any) => {
            if (err && !hasEnded) {
              console.error('Python shell end error:', err);
              hasEnded = true;
              reject(err);
            }
          });
        } catch (sendError) {
          console.error('Error sending data to Python:', sendError);
          if (!hasEnded) {
            hasEnded = true;
            reject(sendError);
          }
        }

        // Timeout after 10 seconds
        setTimeout(() => {
          if (!hasEnded) {
            hasEnded = true;
            pyshell.kill();
            reject(new Error('Python ability update timed out'));
          }
        }, 10000);
      });
      
      newAbility = (abilityResult as any[])[0]?.updated_ability || session.currentAbility;
    } catch (abilityError) {
      console.error('Ability updating failed, using simple fallback:', abilityError);
      // Simple fallback: adjust ability slightly based on correctness
      const adjustment = isCorrect ? 0.1 : -0.1;
      newAbility = session.currentAbility.map(ability => 
        Math.max(-3, Math.min(3, ability + adjustment))
      );
    }

    // 4. 使用Python返回的精确结果更新会话
    session.responses.push(currentResponse);
    session.answeredItems.push(itemId);
    session.recentPerformance.push(isCorrect ? 1 : 0);
    if (session.recentPerformance.length > 10) {
      session.recentPerformance.shift(); // Keep only the last 10
    }
    if (problem.subject in session.subjectQuestionCount) {
      session.subjectQuestionCount[problem.subject]++;
    }
    
    // 🔥 使用MLE计算出的新能力值
    session.currentAbility = newAbility;

    if (session.responses.length >= session.maxItems) {
      session.status = 'completed';
      session.completedAt = new Date().toISOString();
    }

    // ✅ 关键改动: 使用共享的 setSession 函数保存更新
    await setSession(sessionId, session);

    // ✅ 3. 更新返回给前端的结果
    const result = {
      correct: isCorrect,
      feedback: isCorrect ? 'Correct!' : 'Incorrect',
      //correctAnswer: isCorrect ? userAnswer : 'Sample correct answer',
      //solution: session.showSolution ? 'Sample solution explanation' : undefined,
      correctAnswer: session.showSolution ? correctAnswer : undefined,
      solution: session.showSolution ? problem.solution : undefined,
      progress: {
        completed: session.responses.length,
        total: session.maxItems,
        correctCount: session.responses.filter(r => r.correct).length,
        accuracy: Math.round((session.responses.filter(r => r.correct).length / session.responses.length) * 100) || 0,
      },
      updatedAbility: session.currentAbility,
    };

    return NextResponse.json(result);

  } catch (error: any) {
    console.error('Error in /api/test/submit:', error);
    return NextResponse.json({ error: 'Internal server error', details: error.message }, { status: 500 });
  }
}
