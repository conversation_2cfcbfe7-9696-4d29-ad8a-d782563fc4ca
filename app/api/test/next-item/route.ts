// math-adaptive-test/app/api/test/next-item/route.ts

import { NextRequest, NextResponse } from 'next/server';
// +++ 从共享库导入所需函数和类型 +++
import { loadProblems, initializeItemParams, ItemParams, extractBoxedContent, Problem } from '@/lib/data-loader';


interface NextItemRequest {
  currentAbility: number[];      // 6-dimensional ability array
  answeredItems: string[];       // list of answered item IDs
  targetSubject?: string;        // optional target subject
  mode: 'test' | 'practice';    // test mode
  recentPerformance?: number[];  // recent performance (1s and 0s)
  subjectQuestionCount?: Record<string, number>; // question count by subject
  questionIndex?: number;        // specific question index for sequential mode
}

// 为响应对象定义更灵活的类型
interface NextItemResponse {
  itemId: string;
  problem: string;
  subject: string;
  difficulty: number;
  level: string;
  type: string;
  solution?: string;      // 可选属性
  correctAnswer?: string; // 可选属性
}

// Subject mapping
const SUBJECT_MAPPING: Record<string, number> = {
  'algebra': 0,
  'counting_and_probability': 1,
  'geometry': 2,
  'intermediate_algebra': 3,
  'number_theory': 4,
  'prealgebra': 5
};

const SUBJECTS = Object.keys(SUBJECT_MAPPING);






// Calculate probability of correct response (2PL IRT model)
function calculateProbability(theta: number[], itemParams: ItemParams): number {
  const subjectIdx = SUBJECT_MAPPING[itemParams.subject];
  const relevantAbility = theta[subjectIdx];
  
  return 1.0 / (1.0 + Math.exp(-(itemParams.a * relevantAbility - itemParams.b)));
}

// Calculate Fisher information for an item
function calculateInformation(theta: number[], itemParams: ItemParams): number {
  const p = calculateProbability(theta, itemParams);
  const a = itemParams.a;
  
  return (a * a) * p * (1 - p);
}

// Calculate target difficulty based on ability
function calculateTargetDifficulty(ability: number[], targetSubject?: string): number {
  let relevantAbility: number;
  
  if (targetSubject && targetSubject in SUBJECT_MAPPING) {
    const subjectIdx = SUBJECT_MAPPING[targetSubject];
    relevantAbility = ability[subjectIdx];
  } else {
    relevantAbility = ability.reduce((a, b) => a + b, 0) / ability.length;
  }
  
  // Map ability to difficulty level (1-5)
  if (relevantAbility < -1.5) return 1;
  else if (relevantAbility < -0.5) return 2;
  else if (relevantAbility < 0.5) return 3;
  else if (relevantAbility < 1.5) return 4;
  else return 5;
}

// Select next item for test mode (maximum information)
function selectTestItem(
  ability: number[],
  answeredItems: string[],
  availableItems: string[],
  itemParams: Map<string, ItemParams>
): string | null {
  let bestItem: string | null = null;
  let maxInfo = -Infinity;

  for (const itemId of availableItems) {
    const params = itemParams.get(itemId);
    if (!params) continue;

    const info = calculateInformation(ability, params);
    if (info > maxInfo) {
      maxInfo = info;
      bestItem = itemId;
    }
  }

  return bestItem;
}

// Select next item for practice mode (adaptive difficulty)
function selectPracticeItem(
  ability: number[],
  answeredItems: string[],
  problems: Map<string, Problem>,
  itemParams: Map<string, ItemParams>,
  targetSubject: string | undefined,
  recentPerformance: number[],
  subjectQuestionCount: Record<string, number>
): string | null {
  // If no target subject, choose the least used subject
  if (!targetSubject) {
    const minCount = Math.min(...Object.values(subjectQuestionCount));
    const leastUsedSubjects = SUBJECTS.filter(
      subject => subjectQuestionCount[subject] === minCount
    );
    targetSubject = leastUsedSubjects[Math.floor(Math.random() * leastUsedSubjects.length)];
  }

  // Get available items for the target subject
  let availableItems = Array.from(problems.keys()).filter(
    itemId => !answeredItems.includes(itemId) && 
              problems.get(itemId)?.subject === targetSubject
  );

  // If no items available in target subject, try other subjects
  if (availableItems.length === 0) {
    const allAvailable = Array.from(problems.keys()).filter(
      itemId => !answeredItems.includes(itemId)
    );
    
    if (allAvailable.length === 0) return null;

    // Group by subject and find least used
    const itemsBySubject: Record<string, string[]> = {};
    for (const itemId of allAvailable) {
      const subject = problems.get(itemId)?.subject;
      if (subject) {
        if (!itemsBySubject[subject]) itemsBySubject[subject] = [];
        itemsBySubject[subject].push(itemId);
      }
    }

    // Select from least used subject
    const sortedSubjects = Object.keys(itemsBySubject).sort(
      (a, b) => subjectQuestionCount[a] - subjectQuestionCount[b]
    );
    
    if (sortedSubjects.length > 0) {
      targetSubject = sortedSubjects[0];
      availableItems = itemsBySubject[targetSubject];
    }
  }

  if (availableItems.length === 0) return null;

  // Calculate target difficulty
  let targetDifficulty = calculateTargetDifficulty(ability, targetSubject);

  // Adjust difficulty based on recent performance
  if (recentPerformance.length >= 3) {
    const recentCorrectRate = recentPerformance.slice(-3).reduce((a, b) => a + b, 0) / 3;
    
    if (recentCorrectRate < 0.3) {
      targetDifficulty = Math.max(1, targetDifficulty - 1);
    } else if (recentCorrectRate > 0.8) {
      targetDifficulty = Math.min(5, targetDifficulty + 1);
    }
  }

  // Group items by difficulty difference
  const itemsByDiff: Record<number, string[]> = {};
  for (const itemId of availableItems) {
    const params = itemParams.get(itemId);
    if (!params) continue;
    
    const diff = Math.abs(params.level - targetDifficulty);
    if (!itemsByDiff[diff]) itemsByDiff[diff] = [];
    itemsByDiff[diff].push(itemId);
  }

  // Select randomly from items with minimum difficulty difference
  const minDiff = Math.min(...Object.keys(itemsByDiff).map(Number));
  const candidates = itemsByDiff[minDiff];
  
  if (candidates && candidates.length > 0) {
    return candidates[Math.floor(Math.random() * candidates.length)];
  }

  return null;
}



// API Route Handler
export async function POST(request: NextRequest) {
  try {
    const body: NextItemRequest = await request.json();
    const {
      currentAbility,
      answeredItems,
      targetSubject,
      mode,
      recentPerformance = [],
      subjectQuestionCount = Object.fromEntries(SUBJECTS.map(s => [s, 0])),
      questionIndex
    } = body;

    // Validate input
    if (!Array.isArray(currentAbility) || currentAbility.length !== 6) {
      return NextResponse.json(
        { error: 'currentAbility must be an array of 6 numbers' },
        { status: 400 }
      );
    }

    if (!Array.isArray(answeredItems)) {
      return NextResponse.json(
        { error: 'answeredItems must be an array' },
        { status: 400 }
      );
    }

    if (!['test', 'practice'].includes(mode)) {
      return NextResponse.json(
        { error: 'mode must be either "test" or "practice"' },
        { status: 400 }
      );
    }

    // Load problems and initialize parameters
    const problems = await loadProblems();
    const itemParams = initializeItemParams(problems);

    // Get available items
    const availableItems = Array.from(problems.keys()).filter(
      itemId => !answeredItems.includes(itemId)
    );

    // Filter by target subject if specified
    let filteredItems = availableItems;
    if (targetSubject) {
      filteredItems = availableItems.filter(
        itemId => problems.get(itemId)?.subject === targetSubject
      );
    }

    // Select next item based on mode and questionIndex
    let nextItemId: string | null = null;

    // If questionIndex is specified, use sequential mode
    if (questionIndex !== undefined && questionIndex > 0) {
      try {
        // Get sorted problem list (reuse rendercheck logic)
        const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/rendercheck/list`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ index: questionIndex })
        });

        if (response.ok) {
          const data = await response.json();
          if (data.problem && data.problem.item_id) {
            nextItemId = data.problem.item_id;
          }
        } else if (response.status === 404) {
          // Question index out of range
          return NextResponse.json({
            nextItem: null,
            message: `Question ${questionIndex} not found. End of sequential test.`
          });
        }
      } catch (error) {
        console.error('Error fetching sequential question:', error);
        // Fall back to adaptive mode
      }
    } else {
      // Use adaptive selection
      if (mode === 'test') {
        nextItemId = selectTestItem(currentAbility, answeredItems, filteredItems, itemParams);
      } else {
        nextItemId = selectPracticeItem(
          currentAbility,
          answeredItems,
          problems,
          itemParams,
          targetSubject,
          recentPerformance,
          subjectQuestionCount
        );
      }
    }

    // If no item found, return null
    if (!nextItemId) {
      return NextResponse.json({
        nextItem: null,
        message: 'No more items available'
      });
    }

    // Get problem details
    const problem = problems.get(nextItemId);
    const params = itemParams.get(nextItemId);

    if (!problem || !params) {
      return NextResponse.json(
        { error: 'Problem not found' },
        { status: 404 }
      );
    }

    // Calculate probability for this item
    const probability = calculateProbability(currentAbility, params);

    // Extract correct answer
    const correctAnswer = extractBoxedContent(problem.solution);

    // Prepare response - 使用新的接口类型
    const nextItemResponse: NextItemResponse = {
      itemId: nextItemId,
      problem: problem.problem,
      subject: problem.subject,
      difficulty: problem.difficulty,
      level: problem.level,
      type: problem.type
    };

    // Include solution only in practice mode
    if (mode === 'practice') {
      nextItemResponse.solution = problem.solution;
      nextItemResponse.correctAnswer = correctAnswer;
    }

    const response = {
      nextItem: nextItemResponse,
      itemParams: {
        discrimination: params.a,
        difficulty: params.b,
        probability: probability
      },
      metadata: {
        totalAvailable: availableItems.length,
        subjectAvailable: filteredItems.length,
        mode: mode
      }
    };

    // Include solution only in practice mode
    if (mode === 'practice') {
      response.nextItem['solution'] = problem.solution;
      response.nextItem['correctAnswer'] = correctAnswer;
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error in /api/test/next-item:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
