// app/api/user/sessions/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { getUserSessions } from '../../test/session-store';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Get user sessions from database
    const sessions = await getUserSessions(userId);
    
    return NextResponse.json({
      sessions,
      count: sessions.length
    });

  } catch (error) {
    console.error('Error fetching user sessions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user sessions' },
      { status: 500 }
    );
  }
}