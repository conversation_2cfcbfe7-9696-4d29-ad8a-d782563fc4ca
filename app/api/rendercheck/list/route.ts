import { NextRequest, NextResponse } from 'next/server';
import { getAllProblems, forceRefreshCache } from '@/lib/data-loader';
import { promises as fs } from 'fs';
import path from 'path';

interface SortedProblem {
  index: number;
  itemId: string;
  subject: string;
  level: string;
  filename: string;
  dataset: string; // train or test
}

const CACHE_FILE = path.join(process.cwd(), 'data', 'sorted-problems-cache.json');

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const forceRefresh = searchParams.get('refresh') === 'true';
    
    // Force refresh cache if requested to ensure latest cleanSolutionText changes
    if (forceRefresh) {
      forceRefreshCache();
    }
    
    // Try to load from cache first
    if (!forceRefresh) {
      try {
        const cacheContent = await fs.readFile(CACHE_FILE, 'utf-8');
        const cachedData = JSON.parse(cacheContent);
        
        // Check if cache is recent (less than 1 hour old)
        const cacheAge = Date.now() - new Date(cachedData.generatedAt).getTime();
        if (cacheAge < 60 * 60 * 1000) { // 1 hour
          return NextResponse.json({
            problems: cachedData.problems,
            total: cachedData.total,
            fromCache: true,
            generatedAt: cachedData.generatedAt
          });
        }
      } catch (error) {
        // Cache doesn't exist or is invalid, continue to generate
        console.log('Cache not found or invalid, generating new list...');
      }
    }
    
    // Load all problems
    const allProblems = await getAllProblems();
    
    // Create sorted list based on filename
    const sortedProblems: SortedProblem[] = [];
    
    allProblems.forEach(problem => {
      // Extract dataset, subject, and filename from item_id
      // Format: dataset_subject_filename
      // Note: subject can contain underscores (e.g., counting_and_probability)
      // The item_id is created as: `${dataset}_${subject}_${file.replace('.json', '')}`
      
      // Extract dataset (first part: train or test)
      let dataset, filename;
      if (problem.item_id.startsWith('train_')) {
        dataset = 'train';
        const afterDataset = problem.item_id.substring(6); // Remove 'train_'
        const subjectPrefix = `${problem.subject}_`;
        if (afterDataset.startsWith(subjectPrefix)) {
          filename = afterDataset.substring(subjectPrefix.length) + '.json';
        } else {
          filename = afterDataset + '.json';
        }
      } else if (problem.item_id.startsWith('test_')) {
        dataset = 'test';
        const afterDataset = problem.item_id.substring(5); // Remove 'test_'
        const subjectPrefix = `${problem.subject}_`;
        if (afterDataset.startsWith(subjectPrefix)) {
          filename = afterDataset.substring(subjectPrefix.length) + '.json';
        } else {
          filename = afterDataset + '.json';
        }
      } else {
        dataset = 'unknown';
        filename = problem.item_id + '.json';
      }
      
      const subject = problem.subject;
        
      sortedProblems.push({
        index: 0, // Will be set after sorting
        itemId: problem.item_id,
        subject,
        level: problem.level,
        filename,
        dataset
      });
    });
    
    // Sort by dataset, then subject, then filename
    sortedProblems.sort((a, b) => {
      // First by dataset (test before train for consistency)
      if (a.dataset !== b.dataset) {
        return a.dataset.localeCompare(b.dataset);
      }
      
      // Then by subject
      if (a.subject !== b.subject) {
        return a.subject.localeCompare(b.subject);
      }
      
      // Finally by filename (natural sort for numbers)
      return naturalSort(a.filename, b.filename);
    });
    
    // Assign indices (1-based)
    sortedProblems.forEach((problem, index) => {
      problem.index = index + 1;
    });
    
    // Prepare response data
    const responseData = {
      problems: sortedProblems,
      total: sortedProblems.length,
      fromCache: false,
      generatedAt: new Date().toISOString(),
      stats: {
        byDataset: sortedProblems.reduce((acc, p) => {
          acc[p.dataset] = (acc[p.dataset] || 0) + 1;
          return acc;
        }, {} as Record<string, number>),
        bySubject: sortedProblems.reduce((acc, p) => {
          acc[p.subject] = (acc[p.subject] || 0) + 1;
          return acc;
        }, {} as Record<string, number>)
      }
    };
    
    // Save to cache
    try {
      await fs.mkdir(path.dirname(CACHE_FILE), { recursive: true });
      await fs.writeFile(CACHE_FILE, JSON.stringify(responseData, null, 2));
    } catch (error) {
      console.error('Failed to save cache:', error);
    }
    
    return NextResponse.json(responseData);
    
  } catch (error) {
    console.error('Error generating sorted problem list:', error);
    return NextResponse.json(
      { error: 'Failed to generate problem list' },
      { status: 500 }
    );
  }
}

// Natural sort function for filenames with numbers
function naturalSort(a: string, b: string): number {
  const reA = /[^a-zA-Z]/g;
  const reN = /[^0-9]/g;
  
  const aA = a.replace(reA, '');
  const bA = b.replace(reA, '');
  
  if (aA === bA) {
    const aN = parseInt(a.replace(reN, ''), 10);
    const bN = parseInt(b.replace(reN, ''), 10);
    return aN === bN ? 0 : aN > bN ? 1 : -1;
  } else {
    return aA > bA ? 1 : -1;
  }
}

// API to get a specific problem by index or multiple problems
export async function POST(request: NextRequest) {
  try {
    const { index, perpage } = await request.json();

    if (!index || index < 1) {
      return NextResponse.json(
        { error: 'Invalid index. Index must be >= 1' },
        { status: 400 }
      );
    }

    // Get the sorted list
    const listResponse = await GET(request);
    const listData = await listResponse.json();

    if (index > listData.total) {
      return NextResponse.json(
        { error: `Index ${index} exceeds total problems (${listData.total})` },
        { status: 404 }
      );
    }

    // Load all problem data once
    const allProblems = await getAllProblems();

    // If perpage is specified, return multiple problems
    if (perpage && perpage > 1) {
      const endIndex = Math.min(index + perpage - 1, listData.total);
      const problems = [];

      for (let i = index; i <= endIndex; i++) {
        const targetProblem = listData.problems.find((p: SortedProblem) => p.index === i);
        if (targetProblem) {
          const fullProblem = allProblems.find(p => p.item_id === targetProblem.itemId);
          if (fullProblem) {
            problems.push({
              index: i,
              problem: fullProblem,
              filename: targetProblem.filename,
              dataset: targetProblem.dataset
            });
          }
        }
      }

      return NextResponse.json({
        startIndex: index,
        endIndex,
        perpage,
        total: listData.total,
        problems,
        navigation: {
          hasPrevious: index > 1,
          hasNext: endIndex < listData.total,
          previousStartIndex: index > 1 ? Math.max(1, index - perpage) : null,
          nextStartIndex: endIndex < listData.total ? endIndex + 1 : null
        }
      });
    }

    // Single problem mode (original behavior)
    const targetProblem = listData.problems.find((p: SortedProblem) => p.index === index);

    if (!targetProblem) {
      return NextResponse.json(
        { error: `Problem not found at index ${index}` },
        { status: 404 }
      );
    }

    const fullProblem = allProblems.find(p => p.item_id === targetProblem.itemId);

    if (!fullProblem) {
      return NextResponse.json(
        { error: `Problem data not found for ${targetProblem.itemId}` },
        { status: 404 }
      );
    }

    return NextResponse.json({
      index,
      total: listData.total,
      problem: fullProblem,
      filename: targetProblem.filename,
      dataset: targetProblem.dataset,
      navigation: {
        hasPrevious: index > 1,
        hasNext: index < listData.total,
        previousIndex: index > 1 ? index - 1 : null,
        nextIndex: index < listData.total ? index + 1 : null
      }
    });

  } catch (error) {
    console.error('Error getting problem by index:', error);
    return NextResponse.json(
      { error: 'Failed to get problem' },
      { status: 500 }
    );
  }
}
