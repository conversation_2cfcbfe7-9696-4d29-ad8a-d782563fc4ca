// app/auth/callback/page.tsx
'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase'
import { Loader2 } from 'lucide-react'

export default function AuthCallback() {
  const router = useRouter()
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    let mounted = true
    let timeoutId: NodeJS.Timeout
    let authSubscription: { data: { subscription: any } } | null = null


    const handleAuthCallback = async () => {
      try {
        console.log('Auth callback page loaded, URL:', window.location.href)
        console.log('URL hash:', window.location.hash)
        console.log('URL search:', window.location.search)
        console.log('Supabase client configured')

        // Set up auth state listener before processing the callback
        authSubscription = supabase.auth.onAuthStateChange((event, session) => {
          if (!mounted) return

          console.log('Auth state change in callback:', event, session?.user?.email || 'no user')

          if (event === 'SIGNED_IN' && session) {
            console.log('Authentication successful via auth listener, redirecting...')
            // Clear timeout since we're successfully authenticated
            if (timeoutId) clearTimeout(timeoutId)
            // Clean up the URL and redirect immediately
            setTimeout(() => {
              window.location.href = '/'
            }, 500)
          } else if (event === 'TOKEN_REFRESHED' && session) {
            console.log('Token refreshed successfully, redirecting...')
            if (timeoutId) clearTimeout(timeoutId)
            setTimeout(() => {
              window.location.href = '/'
            }, 500)
          }
        })

        // Check if URL contains OAuth tokens and process them manually
        const urlHash = window.location.hash
        const urlSearch = window.location.search
        
        if (urlHash.includes('access_token')) {
          console.log('OAuth access token detected in URL hash, processing...')
          
          // Parse the hash parameters manually
          const hashParams = new URLSearchParams(urlHash.substring(1))
          const accessToken = hashParams.get('access_token')
          const refreshToken = hashParams.get('refresh_token')
          const expiresIn = hashParams.get('expires_in')
          const expiresAt = hashParams.get('expires_at')
          const tokenType = hashParams.get('token_type')
          
          console.log('Parsed tokens:', { 
            hasAccessToken: !!accessToken, 
            hasRefreshToken: !!refreshToken,
            expiresIn,
            expiresAt,
            tokenType
          })
          
          // If we have tokens, try to manually set the session
          if (accessToken && refreshToken) {
            try {
              console.log('Attempting to set session manually...')
              
              // Use Supabase's setSession method to manually create the session
              const { data, error } = await supabase.auth.setSession({
                access_token: accessToken,
                refresh_token: refreshToken
              })
              
              if (error) {
                console.error('Error setting session manually:', error)
              } else if (data.session) {
                console.log('Session set manually successfully:', data.session.user?.email)
                // Clear any existing timeout
                if (timeoutId) clearTimeout(timeoutId)
                // Show success message briefly before redirect
                setError('Sign in successful! Redirecting...')
                // Use direct window.location redirect instead of Next.js router
                console.log('Redirecting to home page...')
                setTimeout(() => {
                  window.location.href = '/'
                }, 1000)
                return
              }
            } catch (setSessionError) {
              console.error('Exception during manual session setting:', setSessionError)
            }
          }
          
          // If manual setting didn't work, give Supabase time to auto-process
          console.log('Manual session setting failed or incomplete, waiting for auto-processing...')
          await new Promise(resolve => setTimeout(resolve, 2000))
        } else if (urlSearch.includes('code')) {
          console.log('OAuth code detected in URL search, processing...')
          await new Promise(resolve => setTimeout(resolve, 1500))
        }

        // Now check for session
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession()
        
        if (!mounted) return

        if (sessionError) {
          console.error('Auth session error:', sessionError)
          setError(`Authentication failed: ${sessionError.message}`)
          timeoutId = setTimeout(() => {
            if (mounted) router.push('/?error=auth-failed')
          }, 2000)
          return
        }

        if (sessionData.session) {
          console.log('Session found after processing, redirecting...')
          setTimeout(() => {
            window.location.href = '/'
          }, 500)
          return
        } else if (urlHash.includes('access_token')) {
          // If no session but we have tokens in URL, try manual processing one more time
          console.log('No session found but tokens in URL, trying manual processing...')
          
          try {
            // Force a session refresh to pick up the tokens
            const { error: refreshError } = await supabase.auth.refreshSession()
            if (!refreshError) {
              // Check one more time
              const { data: finalCheck } = await supabase.auth.getSession()
              if (finalCheck.session) {
                console.log('Session found after manual refresh')
                setTimeout(() => {
                  window.location.href = '/'
                }, 500)
                return
              }
            }
          } catch (refreshError) {
            console.error('Manual refresh failed:', refreshError)
          }
        }

        // If we still don't have a session but have access tokens in URL, force redirect
        if (urlHash.includes('access_token')) {
          console.log('Access token detected but no session established - forcing redirect in 3 seconds')
          setTimeout(() => {
            if (!mounted) return
            console.log('Force redirecting due to access token presence...')
            window.location.href = '/?auth=pending'
          }, 3000)
        }

        // Set timeout for authentication completion
        timeoutId = setTimeout(() => {
          if (!mounted) return
          console.log('Authentication timed out after 8 seconds')
          setError('Authentication timed out. Please try signing in again.')
          setTimeout(() => {
            if (mounted) {
              window.location.href = '/?error=auth-timeout'
            }
          }, 2000)
        }, 8000) // 8 second timeout (shorter since we have fallbacks)

        console.log('Waiting for authentication to complete...')

      } catch (error) {
        if (!mounted) return

        console.error('Auth callback error:', error)
        setError('Authentication error occurred. Please try again.')
        timeoutId = setTimeout(() => {
          if (mounted) router.push('/?error=callback-error')
        }, 2000)
      }
    }

    // Start the auth callback handling
    handleAuthCallback()

    return () => {
      mounted = false
      if (timeoutId) clearTimeout(timeoutId)
      if (authSubscription) {
        authSubscription.data.subscription.unsubscribe()
      }
    }
  }, [router])

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        {!error && <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />}
        <h2 className="text-lg font-semibold mb-2">
          {error?.includes('successful') ? 'Sign In Successful!' : 
           error ? 'Authentication Error' : 'Completing sign in...'}
        </h2>
        <p className="text-muted-foreground">
          {error || 'Please wait while we finish signing you in.'}
        </p>
        {error && (
          <p className="text-sm text-muted-foreground mt-2">
            Redirecting you back to the home page...
          </p>
        )}
      </div>
    </div>
  )
}