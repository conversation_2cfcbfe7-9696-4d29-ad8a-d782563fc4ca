# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development
- `pnpm dev` - Start development server with turbopack
- `pnpm build` - Build production version
- `pnpm start` - Start production server
- `pnpm lint` - Run Next.js linting

### Dependencies
- Uses `pnpm` as package manager (pnpm-lock.yaml present)
- No test framework configuration found - verify test setup before running tests

## Architecture

This is a **Next.js 15 + React 19** adaptive mathematics testing platform that combines:

### Core Technologies
- **Frontend**: Next.js App Router, React 19, TypeScript, Tailwind CSS
- **UI Components**: Radix UI primitives with shadcn/ui styling
- **Math Rendering**: better-react-mathjax for LaTeX math expressions
- **Backend**: Next.js API routes with adaptive test engine
- **Data Processing**: Python scripts for ability estimation using scipy optimization

### Application Structure

#### Frontend Pages
- `app/page.tsx` - Landing page with test configuration and subject selection
- `app/test/page.tsx` - Main test interface with adaptive questioning
- `app/results/page.tsx` - Results and analytics page

#### API Architecture (`app/api/test/`)
- `start/route.ts` - Initialize new test sessions
- `next-item/route.ts` - Adaptive item selection using IRT algorithms
- `submit/route.ts` - Answer processing and ability estimation
- `results/route.ts` - Generate performance reports
- `session-store.ts` - In-memory session management

#### Data Layer
- `lib/data-loader.ts` - MATH dataset loading and problem caching
- `data/MATH/train/` - Mathematical problems organized by subject (algebra, geometry, etc.)
- Problem format: JSON files with problem text, solutions, and difficulty levels

#### Python Integration
- `python/ability_updater.py` - Maximum likelihood estimation for ability updates
- `python/answer_checker.py` - Mathematical answer equivalence checking
- Uses scipy for optimization and numerical computation

### Key Features

#### Adaptive Testing Engine
- **Item Response Theory (IRT)**: 2-parameter logistic model for item selection
- **Multi-dimensional abilities**: Tracks separate abilities for 6 math subjects
- **Adaptive difficulty**: Adjusts question difficulty based on performance
- **Session persistence**: Maintains test state across page reloads

#### Mathematical Content Processing
- **LaTeX rendering**: Handles complex mathematical notation with better-react-mathjax
- **Mixed content parsing**: Processes HTML, LaTeX, and tabular data
- **Answer extraction**: Parses boxed answers from solution text
- **Content normalization**: Cleans and formats mathematical expressions

#### User Experience
- **Two modes**: Formal test (no solutions) vs Practice (with solutions/explanations)
- **Real-time feedback**: Immediate answer validation and progress tracking
- **Time management**: Optional time limits with countdown timers
- **Responsive design**: Works across desktop and mobile devices

### Subject Areas
Mathematics problems cover 6 subjects:
- Algebra
- Counting & Probability  
- Geometry
- Intermediate Algebra
- Number Theory
- Pre-algebra

### Development Notes
- Uses dynamic imports for LaTeX components to avoid SSR issues
- Implements comprehensive error boundaries and loading states
- Session data stored in memory (consider persistent storage for production)
- Python scripts called via child_process for scientific computing
- Mathematical answer checking handles various equivalent forms (fractions, decimals, etc.)
