# Supabase Google登录设置指南

这个指南将帮助你配置Supabase Google OAuth登录功能和数据库存储。

## 1. Supabase项目设置

### 创建Supabase项目
1. 访问 [Supabase Dashboard](https://supabase.com/dashboard)
2. 点击 "New Project"
3. 填写项目信息并创建项目
4. 等待项目初始化完成

### 获取API密钥
1. 在项目仪表板中，转到 **Settings > API**
2. 复制以下信息：
   - Project URL (项目URL)
   - anon public key (匿名公钥)
   - service_role key (服务角色密钥)

## 2. 环境变量配置

在项目根目录创建 `.env.local` 文件，填入从 `supabase.env` 中的配置：

```env
# Supabase配置
NEXT_PUBLIC_SUPABASE_URL=你的supabase项目URL
NEXT_PUBLIC_SUPABASE_ANON_KEY=你的supabase匿名密钥
SUPABASE_SERVICE_ROLE_KEY=你的supabase服务角色密钥
```

## 3. Google OAuth设置

### 在Google Cloud Console配置
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 Google+ API
4. 转到 **APIs & Services > Credentials**
5. 点击 **Create Credentials > OAuth 2.0 Client IDs**
6. 选择 "Web application"
7. 添加授权回调URI：
   ```
   https://你的项目ID.supabase.co/auth/v1/callback
   ```
8. 保存Client ID和Client Secret

### 在Supabase中配置Google OAuth
1. 在Supabase仪表板中，转到 **Authentication > Providers**
2. 找到 Google 提供商
3. 启用Google登录
4. 填入从Google Cloud Console获取的：
   - Client ID
   - Client Secret
5. 保存配置

## 4. 数据库Schema设置

在Supabase SQL编辑器中运行 `database-schema.sql` 文件中的SQL语句：

1. 转到 **SQL Editor** 
2. 创建新查询
3. 复制粘贴 `database-schema.sql` 文件内容
4. 点击运行

这将创建以下表：
- `user_profiles` - 用户资料表
- `test_sessions` - 测试会话表

## 5. 测试认证流程

1. 启动开发服务器：
   ```bash
   pnpm dev
   ```

2. 访问 `http://localhost:3000`

3. 点击 "Sign in with Google" 按钮

4. 完成Google OAuth流程

5. 验证以下功能：
   - 用户登录成功
   - 用户资料创建
   - 导航栏显示用户头像
   - 访问个人资料页面 `/profile`

## 6. 功能特性

### 已实现的功能
✅ Google OAuth登录/登出  
✅ 用户资料管理  
✅ 测试会话数据库存储  
✅ 用户会话历史记录  
✅ 个人资料页面  
✅ 响应式设计  
✅ 数据持久化  

### 数据库功能
- 用户测试会话自动保存到Supabase
- 支持恢复未完成的测试
- 用户历史记录和统计
- 行级安全策略(RLS)

## 7. 文件结构

新增的主要文件：
```
lib/
├── supabase.ts              # Supabase客户端配置
├── auth-context.tsx         # React认证上下文

components/auth/
├── auth-button.tsx          # 登录/登出按钮组件

app/
├── auth/callback/page.tsx   # OAuth回调页面
├── profile/page.tsx         # 用户个人资料页面

hooks/
├── use-toast.ts            # Toast通知hook
```

## 8. 安全注意事项

- 所有敏感的API密钥都存储在环境变量中
- 实现了行级安全策略(RLS)
- 用户只能访问自己的数据
- 服务角色密钥仅用于服务器端操作

## 9. 故障排除

### 常见问题

**问题**: Google登录失败
**解决**: 检查Google Cloud Console中的回调URL设置

**问题**: 数据库连接错误
**解决**: 验证环境变量是否正确设置

**问题**: 用户资料未创建
**解决**: 确保数据库触发器已正确创建

### 调试技巧
- 查看浏览器控制台错误信息
- 检查Supabase仪表板日志
- 验证环境变量加载

## 10. 生产部署

部署到生产环境时：
1. 更新Google OAuth回调URL为生产域名
2. 设置生产环境变量
3. 确保数据库schema已应用
4. 测试完整的认证流程

---

现在你的数学竞赛练习网站已经集成了完整的Google登录功能和Supabase数据库存储！

用户可以：
- 使用Google账号登录
- 保存和恢复测试进度
- 查看个人统计和历史记录
- 跨设备同步数据