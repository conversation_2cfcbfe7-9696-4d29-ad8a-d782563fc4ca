This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
# math-adaptive-test
# CLAUDE.md

This file provides guidance to Claude Code (claude.ai/code) when working with code in this repository.

## Commands

### Development
- `pnpm dev` - Start development server with turbopack
- `pnpm build` - Build production version
- `pnpm start` - Start production server
- `pnpm lint` - Run Next.js linting

### Dependencies
- Uses `pnpm` as package manager (pnpm-lock.yaml present)
- No test framework configuration found - verify test setup before running tests

## Architecture

This is a **Next.js 15 + React 19** adaptive mathematics testing platform that combines:

### Core Technologies
- **Frontend**: Next.js App Router, React 19, TypeScript, Tailwind CSS
- **UI Components**: Radix UI primitives with shadcn/ui styling
- **Math Rendering**: KaTeX via react-katex for LaTeX math expressions
- **Backend**: Next.js API routes with adaptive test engine
- **Data Processing**: Python scripts for ability estimation using scipy optimization

### Application Structure

#### Frontend Pages
- `app/page.tsx` - Landing page with test configuration and subject selection
- `app/test/page.tsx` - Main test interface with adaptive questioning
- `app/results/page.tsx` - Results and analytics page

#### API Architecture (`app/api/test/`)
- `start/route.ts` - Initialize new test sessions
- `next-item/route.ts` - Adaptive item selection using IRT algorithms
- `submit/route.ts` - Answer processing and ability estimation
- `results/route.ts` - Generate performance reports
- `session-store.ts` - In-memory session management

#### Data Layer
- `lib/data-loader.ts` - MATH dataset loading and problem caching
- `data/MATH/train/` - Mathematical problems organized by subject (algebra, geometry, etc.)
- Problem format: JSON files with problem text, solutions, and difficulty levels

#### Python Integration
- `python/ability_updater.py` - Maximum likelihood estimation for ability updates
- `python/answer_checker.py` - Mathematical answer equivalence checking
- Uses scipy for optimization and numerical computation

### Key Features

#### Adaptive Testing Engine
- **Item Response Theory (IRT)**: 2-parameter logistic model for item selection
- **Multi-dimensional abilities**: Tracks separate abilities for 6 math subjects
- **Adaptive difficulty**: Adjusts question difficulty based on performance
- **Session persistence**: Maintains test state across page reloads

#### Mathematical Content Processing
- **LaTeX rendering**: Handles complex mathematical notation with KaTeX
- **Mixed content parsing**: Processes HTML, LaTeX, and tabular data
- **Answer extraction**: Parses boxed answers from solution text
- **Content normalization**: Cleans and formats mathematical expressions

#### User Experience
- **Two modes**: Formal test (no solutions) vs Practice (with solutions/explanations)
- **Real-time feedback**: Immediate answer validation and progress tracking
- **Time management**: Optional time limits with countdown timers
- **Responsive design**: Works across desktop and mobile devices

### Subject Areas
Mathematics problems cover 6 subjects:
- Algebra
- Counting & Probability  
- Geometry
- Intermediate Algebra
- Number Theory
- Pre-algebra

### Development Notes
- Uses dynamic imports for LaTeX components to avoid SSR issues
- Implements comprehensive error boundaries and loading states
- Session data stored in memory (consider persistent storage for production)
- Python scripts called via child_process for scientific computing
- Mathematical answer checking handles various equivalent forms (fractions, decimals, etc.)