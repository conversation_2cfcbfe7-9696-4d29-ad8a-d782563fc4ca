// lib/data-loader.ts
import { promises as fs } from 'fs';
import path from 'path';

// Types
export interface Problem {
  problem: string;
  level: string;
  type: string;
  solution: string;
  subject: string;
  difficulty: number;
  item_id: string;
  correctAnswer?: string;
}

// +++ 新增 ItemParams 接口并导出 +++
export interface ItemParams {
  a: number;      // 区分度 (discrimination)
  b: number;      // 难度参数 (difficulty parameter)
  subject: string;
  level: number;
}
// Subject mapping
const SUBJECT_MAPPING: Record<string, number> = {
  'algebra': 0,
  'counting_and_probability': 1,
  'geometry': 2,
  'intermediate_algebra': 3,
  'number_theory': 4,
  'prealgebra': 5
};

const SUBJECTS = Object.keys(SUBJECT_MAPPING);

// Cache for problems to avoid repeated file reads
let problemsCache: Map<string, Problem> | null = null;
// +++ 新增 itemParamsCache +++
let itemParamsCache: Map<string, ItemParams> | null = null;


// +++ 导出 loadProblems 函数，以便 submit 路由可以调用它 +++
export async function loadProblems(): Promise<Map<string, Problem>> {
  if (problemsCache) return problemsCache;

  const problems = new Map<string, Problem>();
  const mathDataPath = path.join(process.cwd(), 'data', 'MATH');
  
  // Load from both train and test directories
  const datasets = ['train', 'test'];
  
  for (const dataset of datasets) {
    const dataPath = path.join(mathDataPath, dataset);
    
    // Check if directory exists
    try {
      await fs.access(dataPath);
    } catch {
      console.warn(`Directory ${dataPath} does not exist, skipping...`);
      continue;
    }

    for (const subject of SUBJECTS) {
      const subjectPath = path.join(dataPath, subject);
      
      try {
        const files = await fs.readdir(subjectPath);
        
        for (const file of files) {
          if (file.endsWith('.json')) {
            const filePath = path.join(subjectPath, file);
            const content = await fs.readFile(filePath, 'utf-8');
            const problemData = JSON.parse(content);
            
            // Include dataset (train/test) in the item ID
            const itemId = `${dataset}_${subject}_${file.replace('.json', '')}`;
            const difficulty = parseDifficulty(problemData.level || 'Level 1');
            
            // Extract correct answer from solution using the same function as next-item API
            const correctAnswer = extractBoxedContent(problemData.solution);
            
            const cleanedSolution = cleanSolutionText(problemData.solution);
            
            problems.set(itemId, {
              ...problemData,
              subject,
              difficulty,
              item_id: itemId,
              correctAnswer,
              solution: cleanedSolution
            });
          }
        }
      } catch (error) {
        console.error(`Error loading problems for ${dataset}/${subject}:`, error);
      }
    }
  }

  problemsCache = problems;
  return problems;
}

// Convert LaTeX tabular environment to HTML table
function convertTabularToHtml(tabularLatex: string): string {
  // Extract the column specification and content
  const tabularMatch = tabularLatex.match(/\\begin\{tabular\}\{([^}]+)\}([\s\S]*?)\\end\{tabular\}/);
  if (!tabularMatch) return tabularLatex; // Return original if can't parse
  
  const [, colSpec, content] = tabularMatch;
  
  // More robust parsing: handle the exact structure we see in the data
  // First, normalize line breaks and remove \hline entries
  const cleanContent = content
    .replace(/\\\\\s*\\hline\s*/g, '\\\\') // Remove \hline after \\
    .replace(/\\hline\s*/g, '') // Remove standalone \hline
    .trim();
  
  // Split by \\ and clean up each row
  const rows = cleanContent
    .split('\\\\')
    .map(row => row.trim())
    .filter(row => row && !row.includes('\\hline'));
  
  // Build HTML table
  let html = '<table class="math-table" style="border-collapse: collapse; margin: 1em auto; text-align: center;">\n';
  
  rows.forEach((row, rowIndex) => {
    // Split row into cells
    const cells = row.split('&').map(cell => cell.trim());
    
    html += '  <tr>\n';
    cells.forEach(cell => {
      // Clean cell content first - remove unwanted LaTeX commands
      let cellContent = cell
        .replace(/\\vspace\{[^}]*\}\s*/g, '') // Remove vspace commands
        .replace(/\\allowbreak\s*/g, ''); // Remove allowbreak commands
      
      // Wrap math expressions in $ if they aren't already
      if (cellContent && !cellContent.includes('$')) {
        // Check if it looks like math content that should be rendered as math
        // This includes: LaTeX commands, or patterns like "A,B", "A,C", etc.
        if (cellContent.includes('\\') || /^[A-Z],[A-Z]$/.test(cellContent) || /^[a-zA-Z0-9\-+()=]+$/.test(cellContent)) {
          cellContent = `$${cellContent}$`;
        }
      }
      
      const style = rowIndex === 0 ? 
        'border: 1px solid #ccc; padding: 8px; font-weight: bold; background-color: #f5f5f5;' :
        'border: 1px solid #ccc; padding: 8px;';
      
      html += `    <td style="${style}">${cellContent}</td>\n`;
    });
    html += '  </tr>\n';
  });
  
  html += '</table>';
  
  return html;
}

// Clean and format solution text
function cleanSolutionText(solution: string): string {
  if (!solution) return '';
  
  let cleaned = solution;
  
  // Preserve array and tabular environments - don't add spaces inside them
  const arrayMatches: Array<{match: string, placeholder: string}> = [];
  let arrayIndex = 0;
  
  // Extract and preserve array/tabular environments
  cleaned = cleaned.replace(/\\begin\{(array|tabular)\}[\s\S]*?\\end\{\1\}/g, (match) => {
    const placeholder = `__ARRAY_${arrayIndex}__`;
    arrayMatches.push({ match, placeholder });
    arrayIndex++;
    return placeholder;
  });
  
  // Normalize fraction formats for consistent rendering
  // Handle \frac25 -> \frac{2}{5} (two consecutive digits without space)
  cleaned = cleaned.replace(/\\frac(\d)(\d)/g, '\\frac{$1}{$2}');

  // Handle \frac 25 -> \frac{2}{5} (two consecutive digits with space)
  cleaned = cleaned.replace(/\\frac\s+(\d)(\d)/g, '\\frac{$1}{$2}');

  // Handle \frac ab -> \frac{a}{b} (two consecutive single characters without space)
  cleaned = cleaned.replace(/\\frac([a-zA-Z])([a-zA-Z])/g, '\\frac{$1}{$2}');

  // Handle \frac ab -> \frac{a}{b} (two consecutive single characters with space)
  cleaned = cleaned.replace(/\\frac\s+([a-zA-Z])([a-zA-Z])/g, '\\frac{$1}{$2}');

  // Handle \frac a b -> \frac{a}{b} (single characters with space)
  cleaned = cleaned.replace(/\\frac\s+([a-zA-Z0-9])\s+([a-zA-Z0-9])/g, '\\frac{$1}{$2}');

  // Normalize fractions inside \boxed{} to have consistent height with regular fractions
  // Add \textstyle to fractions inside \boxed to match inline fraction height
  cleaned = cleaned.replace(/\\boxed\{([^}]*\\frac\{[^}]+\}\{[^}]+\}[^}]*)\}/g, (_, content) => {
    // Add \textstyle before fractions inside boxed
    const normalizedContent = content.replace(/\\frac\{([^}]+)\}\{([^}]+)\}/g, '\\textstyle\\frac{$1}{$2}');
    return `\\boxed{${normalizedContent}}`;
  });

  // Fix consecutive inline math expressions, but preserve display math ($$...$$)
  // First, protect ALL math expressions (display math first, then inline math)
  const displayMathMatches: Array<{placeholder: string, content: string}> = [];
  const inlineMathMatches: Array<{placeholder: string, content: string}> = [];
  let displayMathIndex = 0;
  let inlineMathIndex = 0;

  // Extract display math expressions ($$...$$) first
  cleaned = cleaned.replace(/\$\$[^$]*\$\$/g, (match) => {
    const placeholder = `__DISPLAYMATH_${displayMathIndex}__`;
    displayMathMatches.push({ placeholder, content: match });
    displayMathIndex++;
    return placeholder;
  });

  // Extract inline math expressions ($...$)
  cleaned = cleaned.replace(/\$[^$]+\$/g, (match) => {
    const placeholder = `__INLINEMATH_${inlineMathIndex}__`;
    inlineMathMatches.push({ placeholder, content: match });
    inlineMathIndex++;
    return placeholder;
  });

  // Now fix consecutive math placeholders (this shouldn't happen now, but keep for safety)
  cleaned = cleaned.replace(/(__INLINEMATH_\d+__)(__INLINEMATH_\d+__)/g, '$1 $2');
  
  // Fix specific concatenation patterns (now safe from math content)
  cleaned = cleaned.replace(/π\./g, 'π. ');
  cleaned = cleaned.replace(/π,/g, 'π, ');
  cleaned = cleaned.replace(/\.\s*Next,/g, '. Next, ');
  cleaned = cleaned.replace(/\.\s*Therefore,/g, '. Therefore, ');
  cleaned = cleaned.replace(/\.\s*So\s*/g, '. So ');
  
  // Fix HTML placeholder issues
  cleaned = cleaned.replace(/__HTML_(\d+)__/g, ''); // Remove HTML placeholders for now
  
  // Note: allowbreak removal moved to after math restoration
  
  // Fix spacing around numbers and letters, but be careful with mathematical expressions
  // Only add spaces for clearly non-mathematical contexts (e.g., "3people" -> "3 people")
  // Skip common mathematical patterns like "3ab", "5xy", etc.
  cleaned = cleaned.replace(/([0-9])([A-Za-z]{3,})/g, '$1 $2'); // Only for 3+ letter words (not math variables)
  cleaned = cleaned.replace(/([A-Za-z]{3,})([0-9])/g, '$1 $2'); // Only for 3+ letter words (not math variables)
  
  // Restore inline math expressions first
  inlineMathMatches.forEach(({ placeholder, content }) => {
    // Use function replacement to avoid $$ interpretation issues
    cleaned = cleaned.replace(placeholder, () => content);
  });
  
  // Restore display math expressions last
  displayMathMatches.forEach(({ placeholder, content }) => {
    // Use function replacement to avoid $$ interpretation issues
    cleaned = cleaned.replace(placeholder, () => content);
  });
  
  // Remove \\allowbreak and \\vspace commands that appear as unrendered text in MathJax (after math restoration)
  cleaned = cleaned.replace(/\\allowbreak\s*/g, '');
  cleaned = cleaned.replace(/\\vspace\{[^}]*\}\s*/g, '');
  
  // Fix common LaTeX concatenation issues (now safe - math expressions are restored)
  // Only add spaces around math expressions when they're adjacent to multi-letter words (3+ letters to avoid math variables)
  cleaned = cleaned.replace(/(\$\$[^$]*\$\$)([A-Za-z]{3,})/g, '$1 $2'); // Add space after display math
  cleaned = cleaned.replace(/([A-Za-z]{3,})(\$\$[^$]*\$\$)/g, '$1 $2'); // Add space before display math
  cleaned = cleaned.replace(/(\$[^$]+\$)([A-Za-z]{3,})/g, '$1 $2'); // Add space after inline math
  cleaned = cleaned.replace(/([A-Za-z]{3,})(\$[^$]+\$)/g, '$1 $2'); // Add space before inline math
  
  // Clean up multiple spaces but preserve newlines
  cleaned = cleaned.replace(/[ \t]+/g, ' '); // Only replace spaces and tabs, not newlines
  cleaned = cleaned.replace(/\s+\./g, '.');
  cleaned = cleaned.replace(/\s+,/g, ',');
  
  // Restore array/tabular environments
  arrayMatches.forEach(({ match, placeholder }) => {
    // Convert tabular environments to HTML tables, keep array environments as LaTeX
    if (match.includes('\\begin{tabular}')) {
      const htmlTable = convertTabularToHtml(match);
      cleaned = cleaned.replace(placeholder, () => htmlTable);
    } else {
      // Use function replacement to avoid $$ interpretation issues
      cleaned = cleaned.replace(placeholder, () => match);
    }
  });
  
  return cleaned.trim();
}

// Extract content from \boxed{} with proper brace matching
export function extractBoxedContent(solution: string): string {
  if (!solution) return '';
  
  // Find all \boxed{ occurrences
  const boxedMatches: Array<{index: number, content: string}> = [];
  let searchIndex = 0;
  
  while (true) {
    const boxedIndex = solution.indexOf('\\boxed{', searchIndex);
    if (boxedIndex === -1) break;
    
    let startIndex = boxedIndex + 7; // length of '\boxed{'
    let braceCount = 1;
    let currentIndex = startIndex;
    let content = '';
    
    // Parse until we find the matching closing brace
    while (currentIndex < solution.length && braceCount > 0) {
      const char = solution[currentIndex];
      
      if (char === '\\' && currentIndex + 1 < solution.length) {
        // Handle escaped characters
        content += char + solution[currentIndex + 1];
        currentIndex += 2;
        continue;
      }
      
      if (char === '{') {
        braceCount++;
      } else if (char === '}') {
        braceCount--;
        if (braceCount === 0) {
          // Found the matching closing brace
          boxedMatches.push({ index: boxedIndex, content });
          break;
        }
      }
      
      content += char;
      currentIndex++;
    }
    
    searchIndex = currentIndex + 1;
  }
  
  // Return the last \boxed{} content (usually the final answer)
  return boxedMatches.length > 0 ? boxedMatches[boxedMatches.length - 1].content : '';
}

// +++ 新增并导出 initializeItemParams 函数 +++
export function initializeItemParams(problems: Map<string, Problem>): Map<string, ItemParams> {
  if (itemParamsCache) return itemParamsCache;

  const params = new Map<string, ItemParams>();
  for (const [itemId, problem] of problems) {
    // 这个初始化逻辑与Python脚本中使用的简化模型保持一致
    const initialDifficulty = (problem.difficulty - 3) * 0.5;
    const initialDiscrimination = 1.0; // 固定为1.0以简化

    params.set(itemId, {
      a: initialDiscrimination,
      b: initialDifficulty,
      subject: problem.subject,
      level: problem.difficulty
    });
  }

  itemParamsCache = params;
  return params;
}
// Parse difficulty level from string
function parseDifficulty(levelStr: string): number {
  const match = levelStr.match(/Level (\d+)/);
  return match ? parseInt(match[1]) : 1;
}

// Get a problem by ID
export async function getProblemById(itemId: string): Promise<Problem | undefined> {
  const problems = await loadProblems();
  return problems.get(itemId);
}

// Get all problems
export async function getAllProblems(): Promise<Problem[]> {
  const problems = await loadProblems();
  return Array.from(problems.values());
}

// Get problems by subject
export async function getProblemsBySubject(subject: string): Promise<Problem[]> {
  const problems = await loadProblems();
  return Array.from(problems.values()).filter(p => p.subject === subject);
}

// Clear cache (useful for testing)
export function clearCache(): void {
  problemsCache = null;
  itemParamsCache = null; // +++ 同时清空 itemParams 缓存 +++
}

// Force refresh cache for immediate effect
export function forceRefreshCache(): void {
  clearCache();
}
