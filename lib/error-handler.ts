// lib/error-handler.ts
// Global error handler for server-side errors

if (typeof process !== 'undefined') {
  // Handle uncaught exceptions (like EPIPE errors from Python subprocesses)
  process.on('uncaughtException', (error) => {
    // Log EPIPE errors but don't crash the server
    if (error.message.includes('EPIPE') || (error as any).code === 'EPIPE') {
      console.warn('EPIPE error caught (Python subprocess pipe broken):', error.message);
      // Don't exit the process for EPIPE errors, they're usually recoverable
    } else {
      console.error('Uncaught Exception:', error);
      // For other uncaught exceptions, we might want to handle them differently
      // but avoid crashing the server in development
      if (process.env.NODE_ENV !== 'production') {
        console.error('Non-fatal uncaught exception in development mode');
      }
    }
  });

  // Handle unhandled promise rejections
  process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    // Don't exit the process, just log the error
  });

  // Handle SIGTERM and SIGINT gracefully
  process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    process.exit(0);
  });

  process.on('SIGINT', () => {
    console.log('SIGINT received, shutting down gracefully');
    process.exit(0);
  });
}

export {}; // Make this a module