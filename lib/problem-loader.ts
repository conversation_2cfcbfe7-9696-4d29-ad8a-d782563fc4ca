// lib/problem-loader.ts
// 共享的试题加载和处理逻辑（客户端版本 - 通过API调用）

// 试题响应接口
export interface ProblemResponse {
  itemId: string;
  problem: string;
  subject: string;
  level: string;
  type: string;
  difficulty: number;
  solution?: string;
  correctAnswer?: string;
}

// 试题加载选项
export interface ProblemLoadOptions {
  includeSolution?: boolean;
  includeCorrectAnswer?: boolean;
  mode?: 'test' | 'practice';
}

// 提交答案响应接口
export interface SubmitAnswerResponse {
  correct: boolean;
  correctAnswer: string;
  explanation?: string;
  progress: {
    completed: number;
    total: number;
    correctCount: number;
    accuracy: number;
  };
  updatedAbility: number[];
}

// 通过自适应算法加载下一题（用于test页面）
export async function loadNextAdaptiveProblem(
  currentAbility: number[],
  answeredItems: string[],
  mode: 'test' | 'practice',
  recentPerformance: number[] = [],
  subjectQuestionCount: Record<string, number> = {},
  questionIndex?: number
): Promise<ProblemResponse | null> {
  try {
    const response = await fetch('/api/test/next-item', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        currentAbility,
        answeredItems,
        mode,
        recentPerformance,
        subjectQuestionCount,
        questionIndex
      })
    });

    if (!response.ok) {
      throw new Error('Failed to load next problem');
    }

    const data = await response.json();
    return data.nextItem || null;
  } catch (error) {
    console.error('Error loading next adaptive problem:', error);
    throw new Error('Failed to load next problem');
  }
}

// 通过索引加载试题（用于rendercheck页面）
export async function loadProblemByIndex(
  index: number,
  options: ProblemLoadOptions = {}
): Promise<{
  problem: ProblemResponse;
  filename: string;
  dataset: string;
  navigation: {
    hasPrevious: boolean;
    hasNext: boolean;
    previousIndex: number | null;
    nextIndex: number | null;
  };
  total: number;
} | null> {
  try {
    const response = await fetch('/api/rendercheck/list', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ index })
    });

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error('Failed to load problem by index');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error loading problem by index:', error);
    throw new Error('Failed to load problem by index');
  }
}

// 批量加载试题（用于rendercheck页面的多题模式）
export async function loadProblemsByRange(
  startIndex: number,
  perpage: number,
  options: ProblemLoadOptions = {}
): Promise<{
  startIndex: number;
  endIndex: number;
  perpage: number;
  total: number;
  problems: Array<{
    index: number;
    problem: ProblemResponse;
    filename: string;
    dataset: string;
  }>;
  navigation: {
    hasPrevious: boolean;
    hasNext: boolean;
    previousStartIndex: number | null;
    nextStartIndex: number | null;
  };
} | null> {
  try {
    const response = await fetch('/api/rendercheck/list', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ index: startIndex, perpage })
    });

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error('Failed to load problems by range');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error loading problems by range:', error);
    throw new Error('Failed to load problems by range');
  }
}

// 提交答案（用于test页面）
export async function submitAnswer(
  sessionId: string,
  itemId: string,
  userAnswer: string,
  timeSpent: number
): Promise<SubmitAnswerResponse> {
  try {
    const response = await fetch('/api/test/submit', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        sessionId,
        itemId,
        userAnswer,
        timeSpent
      })
    });

    if (!response.ok) {
      throw new Error('Failed to submit answer');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error submitting answer:', error);
    throw new Error('Failed to submit answer');
  }
}
