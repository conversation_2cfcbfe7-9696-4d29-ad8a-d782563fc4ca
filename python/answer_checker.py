# /python/answer_checker.py
import sys
import json
import re
from fractions import Fraction
import warnings

warnings.filterwarnings('ignore')

# Paste your complete AnswerChecker class here
class AnswerChecker:
    """答案检查器，处理MATH数据集的各种答案格式"""
    
    # 常见的数学单位
    COMMON_UNITS = [
        r'^\circ',  # 度数符号
        r'\\degree', r'°', 'degrees', 'degree', 'deg',  # 度数的各种表示
        'cm', 'mm', 'm', 'km', 'meters', 'meter',  # 长度单位
        'kg', 'g', 'mg', 'grams', 'gram',  # 重量单位
        'ml', 'l', 'liters', 'liter',  # 体积单位
        's', 'sec', 'seconds', 'second', 'min', 'minutes', 'minute', 'h', 'hours', 'hour',  # 时间单位
        r'\$', 'dollars', 'dollar', 'cents', 'cent',  # 货币单位
        '%', 'percent', 'percentage',  # 百分比
        'units', 'unit',  # 通用单位
        'inches', 'inch', 'in', 'ft', 'feet', 'foot',  # 英制单位
        'mph', 'km/h', 'm/s',  # 速度单位
        'square', 'sq', 'cubic',  # 面积/体积修饰
    ]
    
    @staticmethod
    def remove_units(answer: str) -> str:
        """移除答案中的单位"""
        answer = answer.strip()
        for unit in AnswerChecker.COMMON_UNITS:
            answer = re.sub(rf'\s*{re.escape(unit)}\s*$', '', answer, flags=re.IGNORECASE)
            answer = re.sub(rf'^{re.escape(unit)}\s*', '', answer, flags=re.IGNORECASE)
        answer = answer.rstrip('.')
        return answer.strip()
    
    @staticmethod
    def normalize_answer(answer: str) -> str:
        """标准化答案格式"""
        answer = answer.strip()
        answer = ' '.join(answer.split())
        
        # Handle LaTeX text commands - extract text content from \text{...}
        answer = re.sub(r'\\text\{([^}]+)\}', r'\1', answer)
        
        # Handle fractions
        answer = re.sub(r'\\frac\{([^}]+)\}\{([^}]+)\}', r'\1/\2', answer)
        answer = re.sub(r'\\dfrac\{([^}]+)\}\{([^}]+)\}', r'\1/\2', answer)
        
        # Handle LaTeX delimiters
        answer = answer.replace(r'\left(', '(').replace(r'\right)', ')')
        answer = answer.replace(r'\left[', '[').replace(r'\right]', ']')
        answer = answer.replace(r'\left\{', '{').replace(r'\right\}', '}')
        
        # Handle special characters
        answer = answer.replace('−', '-')
        
        return answer

    @staticmethod
    def _evaluate_expression(expr: str):
        """尝试计算表达式的数值"""
        try:
            # Remove thousand separators
            expr = expr.replace(',', '')
            if '/' in expr:
                parts = expr.split('/')
                if len(parts) == 2:
                    return float(parts[0]) / float(parts[1])
            return float(expr)
        except:
            return None

    @staticmethod
    def check_answer(user_answer: str, correct_answer: str) -> bool:
        """检查用户答案是否正确"""
        user_norm = AnswerChecker.normalize_answer(user_answer)
        correct_norm = AnswerChecker.normalize_answer(correct_answer)

        # Direct string comparison after normalization
        if user_norm == correct_norm:
            return True

        # Compare after removing units
        user_no_units = AnswerChecker.remove_units(user_norm)
        correct_no_units = AnswerChecker.remove_units(correct_norm)
        if user_no_units == correct_no_units:
            return True
        
        # Numerical comparison
        try:
            user_val = AnswerChecker._evaluate_expression(user_no_units)
            correct_val = AnswerChecker._evaluate_expression(correct_no_units)
            if user_val is not None and correct_val is not None:
                return abs(user_val - correct_val) < 1e-9
        except:
            pass
            
        return False

# This is the main part that communicates with Node.js
if __name__ == "__main__":
    # The first argument is the script name, so we start from the second.
    # We expect two arguments: user_answer and correct_answer
    if len(sys.argv) != 3:
        # Exit with an error if the number of arguments is wrong
        error_result = {"correct": False, "error": "Invalid number of arguments"}
        print(json.dumps(error_result))
        sys.exit(1)

    user_ans = sys.argv[1]
    correct_ans = sys.argv[2]
    
    is_correct = AnswerChecker.check_answer(user_ans, correct_ans)
    
    # Prepare the result in a JSON format
    result = {"correct": is_correct}
    
    # Print the JSON result to standard output
    print(json.dumps(result))