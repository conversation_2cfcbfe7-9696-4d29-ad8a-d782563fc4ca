"""
Configuration settings for ASY to SVG conversion
"""
import os
from datetime import datetime
from pathlib import Path

# 获取项目根目录（从python目录向上一级）
PROJECT_ROOT = Path(__file__).parent.parent.parent

# 路径配置
DATASET_ROOT = str(PROJECT_ROOT / "data" / "MATH")
SVG_OUTPUT_DIR = str(PROJECT_ROOT / "public" / "images" / "problems")
LOGS_DIR = str(PROJECT_ROOT / "logs")

# Asymptote配置
ASYMPTOTE_CONFIG = {
    'output_format': 'svg',
    'render': 0,
    'imports': ['olympiad', 'cse5', 'aops', 'TrigMacros']
}

# 日志配置
LOG_FILENAME = f"asy-conversion-{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.log"
FAILURE_LOG_FILENAME = f"asy-failures-{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.log"

# 临时文件配置
TEMP_ASY_FILE = "temp_conversion.asy"

# 处理配置
MAX_WORKERS = 4  # 并行处理的最大工作进程数
BACKUP_ORIGINAL = True  # 是否备份原始JSON文件

# 调试信息
print(f"Project root: {PROJECT_ROOT}")
print(f"Dataset root: {DATASET_ROOT}")
print(f"SVG output dir: {SVG_OUTPUT_DIR}")
print(f"Logs dir: {LOGS_DIR}")