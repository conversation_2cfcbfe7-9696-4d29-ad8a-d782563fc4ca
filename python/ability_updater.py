import sys
import json
import numpy as np
from scipy.optimize import minimize
import warnings

# 忽略scipy的运行时警告
warnings.filterwarnings('ignore', category=RuntimeWarning)

class MIRTModelForUpdate:
    """一个简化的MIRT模型，仅用于能力更新计算"""
    def __init__(self, item_params, subject_mapping):
        self.item_params = item_params
        self.subject_mapping = subject_mapping

    def probability(self, theta: np.ndarray, item_id: str) -> float:
        """计算单次回答正确的概率"""
        if item_id not in self.item_params:
            return 0.5  # 安全回退
        
        params = self.item_params[item_id]
        a = params.get('a', 1.0)  # 区分度
        b = params.get('b', 0.0)  # 难度参数
        subject_idx = self.subject_mapping.get(params.get('subject'))

        if subject_idx is None:
            return 0.5 # 安全回退

        relevant_ability = theta[subject_idx]
        
        # IRT 2PL 模型: P(X=1|θ) = 1 / (1 + exp(-(a * θ - b)))
        exponent = -(a * relevant_ability - b)
        
        # 防止计算溢出
        if exponent > 700: return 0.0
        if exponent < -700: return 1.0

        return 1.0 / (1.0 + np.exp(exponent))

def update_ability_mle(current_ability_list, responses, item_params, subject_mapping):
    """使用极大似然估计(MLE)更新能力值"""
    current_ability = np.array(current_ability_list, dtype=float)
    
    if not responses:
        return current_ability.tolist()

    model = MIRTModelForUpdate(item_params, subject_mapping)

    # 定义要最小化的函数：负对数似然函数
    def neg_log_likelihood(theta):
        log_likelihood = 0.0
        for response in responses:
            item_id = response['itemId']
            correct = response['correct']
            
            prob = model.probability(theta, item_id)
            # 限制概率值，防止 log(0)
            prob = np.clip(prob, 1e-9, 1.0 - 1e-9)
            
            if correct:
                log_likelihood += np.log(prob)
            else:
                log_likelihood += np.log(1.0 - prob)
        
        # 添加一个先验分布来正则化，防止能力值发散到无穷大
        # 这假设能力服从均值为0，标准差为2的正态分布，是一种常见的正则化手段
        prior = -np.sum(theta**2) / (2 * 4) 
        
        return -(log_likelihood + prior)

    try:
        # 使用L-BFGS-B优化算法寻找最优的能力值theta
        result = minimize(
            neg_log_likelihood,
            current_ability,
            method='L-BFGS-B',
            bounds=[(-3.5, 3.5)] * len(current_ability) # 限制能力值范围
        )
        
        return result.x.tolist() if result.success else current_ability.tolist()
    except Exception:
        return current_ability.tolist()

if __name__ == "__main__":
    try:
        # 从stdin读取数据而不是命令行参数
        input_line = sys.stdin.readline().strip()
        if not input_line:
            print(json.dumps({"error": "No input data received from stdin."}))
            sys.exit(1)
            
        input_data = json.loads(input_line)
        
        # 确保所有必需的键都存在
        required_keys = ['current_ability', 'responses', 'item_params']
        if not all(key in input_data for key in required_keys):
             print(json.dumps({"error": f"Missing one of required keys: {required_keys}"}))
             sys.exit(1)

        subject_mapping = {
            'algebra': 0, 'counting_and_probability': 1, 'geometry': 2,
            'intermediate_algebra': 3, 'number_theory': 4, 'prealgebra': 5
        }

        updated_ability = update_ability_mle(
            input_data['current_ability'], 
            input_data['responses'], 
            input_data['item_params'],
            subject_mapping
        )
        
        print(json.dumps({"updated_ability": updated_ability}))
        
    except Exception as e:
        print(json.dumps({"error": str(e), "details": "Error processing ability update."}))
        sys.exit(1)