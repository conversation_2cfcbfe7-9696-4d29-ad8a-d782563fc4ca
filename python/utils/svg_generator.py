"""
SVG generation utilities using Asymptote
"""
import os
import subprocess
import tempfile
import logging
from pathlib import Path
from typing import Optional

logger = logging.getLogger(__name__)

class AsymptoteSVGGenerator:
    def __init__(self, temp_dir: Optional[str] = None):
        self.temp_dir = temp_dir or tempfile.gettempdir()
        
    def generate_svg(self, asy_code: str, output_path: str) -> bool:
        """
        Generate SVG from Asymptote code
        
        Args:
            asy_code: Asymptote code string
            output_path: Path where SVG file should be saved
            
        Returns:
            bool: True if successful, False otherwise
        """
        temp_asy_path = None
        try:
            # 创建输出目录
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 创建临时asy文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.asy', 
                                           dir=self.temp_dir, delete=False) as f:
                # 添加必要的导入和设置
                f.write("import settings;\n")
                f.write("settings.outformat = \"svg\";\n")
                f.write("settings.render = 0;\n")
                f.write("import olympiad;\n")
                f.write("import cse5;\n")
                f.write("import aops;\n")
                f.write("import TrigMacros;\n\n")
                f.write(asy_code)
                temp_asy_path = f.name
            
            # 执行asymptote命令
            cmd = ['asy', '-f', 'svg', '-o', output_path, temp_asy_path]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                logger.info(f"Successfully generated SVG: {output_path}")
                return True
            else:
                logger.error(f"Asymptote failed for {output_path}: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error(f"Asymptote timeout for {output_path}")
            return False
        except Exception as e:
            logger.error(f"Error generating SVG {output_path}: {str(e)}")
            return False
        finally:
            # 清理临时文件
            if temp_asy_path and os.path.exists(temp_asy_path):
                try:
                    os.unlink(temp_asy_path)
                except:
                    pass
                    
        return False
        
    def test_asymptote_installation(self) -> bool:
        """
        Test if Asymptote is properly installed
        """
        try:
            result = subprocess.run(['asy', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                logger.info(f"Asymptote version: {result.stdout.strip()}")
                return True
            else:
                logger.error("Asymptote not found or not working properly")
                return False
        except Exception as e:
            logger.error(f"Error testing Asymptote installation: {str(e)}")
            return False