"""
File processing utilities for JSON and text manipulation
"""
import json
import re
import os
import shutil
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional

logger = logging.getLogger(__name__)

class MathDatasetProcessor:
    def __init__(self, svg_generator):
        self.svg_generator = svg_generator
        self.asy_pattern = re.compile(r'\[asy\](.*?)\[/asy\]', re.DOTALL)
        
    def extract_asy_blocks(self, text: str) -> List[str]:
        """
        Extract all [asy]...[/asy] blocks from text
        """
        matches = self.asy_pattern.findall(text)
        return [match.strip() for match in matches]
    
    def process_text_asy_blocks(self, text: str, file_prefix: str, 
                               svg_output_dir: str, block_type: str) -> Tuple[str, List[str]]:
        """
        Process ASY blocks in text and replace with img tags
        
        Args:
            text: Input text containing [asy] blocks
            file_prefix: Prefix for generated SVG files
            svg_output_dir: Directory to save SVG files
            block_type: Type identifier (problem/solution)
            
        Returns:
            Tuple of (processed_text, list_of_failed_conversions)
        """
        asy_blocks = self.extract_asy_blocks(text)
        if not asy_blocks:
            return text, []
        
        result_text = text
        failed_conversions = []
        
        for i, asy_code in enumerate(asy_blocks):
            svg_filename = f"{file_prefix}_{block_type}_{i}.svg"
            svg_path = os.path.join(svg_output_dir, svg_filename)
            relative_path = f"public/images/problems/{svg_filename}"
            
            # 生成SVG
            success = self.svg_generator.generate_svg(asy_code, svg_path)
            
            if success:
                # 替换为img标签
                img_tag = f'<img src="{relative_path}" alt="Mathematical diagram" />'
                # 只替换第一个匹配的asy块
                result_text = self.asy_pattern.sub(img_tag, result_text, count=1)
                logger.debug(f"Converted ASY block to {svg_filename}")
            else:
                failed_conversions.append({
                    'file_prefix': file_prefix,
                    'block_type': block_type,
                    'block_index': i,
                    'asy_code': asy_code,
                    'expected_svg': svg_filename
                })
                logger.warning(f"Failed to convert ASY block in {file_prefix}_{block_type}_{i}")
        
        return result_text, failed_conversions
    
    def process_json_file(self, json_path: Path, file_prefix: str, 
                         svg_output_dir: str, backup: bool = True) -> Dict:
        """
        Process a single JSON file
        
        Returns:
            Dict containing processing statistics
        """
        stats = {
            'file_path': str(json_path),
            'processed': False,
            'problem_asy_blocks': 0,
            'solution_asy_blocks': 0,
            'failed_conversions': [],
            'error': None
        }
        
        try:
            # 备份原文件
            if backup:
                backup_path = json_path.with_suffix('.json.backup')
                if not backup_path.exists():
                    shutil.copy2(json_path, backup_path)
            
            # 读取JSON数据
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            modified = False
            
            # 处理problem字段
            if 'problem' in data and '[asy]' in data['problem']:
                original_text = data['problem']
                processed_text, failed = self.process_text_asy_blocks(
                    original_text, file_prefix, svg_output_dir, 'problem'
                )
                if processed_text != original_text:
                    data['problem'] = processed_text
                    modified = True
                    stats['problem_asy_blocks'] = len(self.extract_asy_blocks(original_text))
                    stats['failed_conversions'].extend(failed)
            
            # 处理solution字段
            if 'solution' in data and '[asy]' in data['solution']:
                original_text = data['solution']
                processed_text, failed = self.process_text_asy_blocks(
                    original_text, file_prefix, svg_output_dir, 'solution'
                )
                if processed_text != original_text:
                    data['solution'] = processed_text
                    modified = True
                    stats['solution_asy_blocks'] = len(self.extract_asy_blocks(original_text))
                    stats['failed_conversions'].extend(failed)
            
            # 保存修改后的JSON
            if modified:
                with open(json_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                logger.info(f"Updated {json_path}")
            
            stats['processed'] = True
            
        except Exception as e:
            stats['error'] = str(e)
            logger.error(f"Error processing {json_path}: {str(e)}")
        
        return stats
    
    def get_file_prefix(self, json_path: Path, dataset_root: str) -> str:
        """
        Generate file prefix based on path structure
        Example: train_algebra_861 or test_geometry_123
        """
        try:
            # 获取相对于dataset_root的路径
            rel_path = json_path.relative_to(Path(dataset_root))
            parts = rel_path.parts  # ('train', 'algebra', '861.json')
            
            split = parts[0]  # train or test
            category = parts[1]  # algebra, geometry, etc.
            filename = parts[2]  # 861.json
            file_id = Path(filename).stem  # 861
            
            return f"{split}_{category}_{file_id}"
        except Exception as e:
            logger.error(f"Error generating file prefix for {json_path}: {e}")
            return f"unknown_{json_path.stem}"