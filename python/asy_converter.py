#!/usr/bin/env python3
"""
ASY to SVG Converter for MATH Dataset

This script converts Asymptote diagrams in JSON files to SVG images
and replaces [asy] blocks with <img> tags.
"""

import argparse
import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from concurrent.futures import ProcessPoolExecutor, as_completed
from typing import List, Dict, Tuple, Optional
import re
import subprocess
import tempfile
import shutil

# Configuration settings
PROJECT_ROOT = Path(__file__).parent.parent
DATASET_ROOT = str(PROJECT_ROOT / "data" / "MATH")
SVG_OUTPUT_DIR = str(PROJECT_ROOT / "public" / "images" / "problems")
LOGS_DIR = str(PROJECT_ROOT / "logs")
# 添加临时ASY文件保存目录
TEMP_ASY_DIR = str(PROJECT_ROOT / "temp_asy_files")
LOG_FILENAME = f"asy-conversion-{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.log"
FAILURE_LOG_FILENAME = f"asy-failures-{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.log"
MAX_WORKERS = 8
# BACKUP_ORIGINAL = True
BACKUP_ORIGINAL = False

print(f"Project root: {PROJECT_ROOT}")
print(f"Dataset root: {DATASET_ROOT}")
print(f"SVG output dir: {SVG_OUTPUT_DIR}")
print(f"Temp ASY dir: {TEMP_ASY_DIR}")
print(f"Logs dir: {LOGS_DIR}")

class AsymptoteSVGGenerator:
    def __init__(self, temp_dir: Optional[str] = None):
        self.temp_dir = temp_dir or tempfile.gettempdir()
        self.logger = logging.getLogger('asy_converter')  # Use custom logger
        # 创建临时ASY文件保存目录
        os.makedirs(TEMP_ASY_DIR, exist_ok=True)
    
    def post_process_asy_code(self, asy_code: str) -> str:
        """Post-process ASY code to add necessary imports and settings"""
        lines_to_add = []
        
        # First, remove any import cse5; statements
        # Using regex to remove import cse5 lines (with possible whitespace variations)
        asy_code = re.sub(r'^\s*import\s+cse5\s*;\s*$', '', asy_code, flags=re.MULTILINE)
        
        # Check if olympiad functions are used but olympiad is not imported
        olympiad_functions = ['rightanglemark', 'foot','anglemark', 'bisectorpoint']
        needs_olympiad = any(func in asy_code for func in olympiad_functions)
        
        if needs_olympiad and 'import olympiad' not in asy_code:
            lines_to_add.append('import olympiad;')
            
            # If importing olympiad, remove any existing import graph; statements
            asy_code = re.sub(r'^\s*import\s+graph\s*;\s*$', '', asy_code, flags=re.MULTILINE)
        
        # Check if neither size() nor unitsize() is called
        # Using regex to check for function calls
        size_pattern = r'\bsize\s*\('
        unitsize_pattern = r'\bunitsize\s*\('
        
        has_size = re.search(size_pattern, asy_code)
        has_unitsize = re.search(unitsize_pattern, asy_code)
        
        if not has_size and not has_unitsize:
            lines_to_add.append('size(300);')
            # lines_to_add.append('size(5cm);')
        
        # Add the lines at the beginning if needed
        if lines_to_add:
            return '\n'.join(lines_to_add) + '\n' + asy_code
        else:
            return asy_code
        
    def generate_svg(self, asy_code: str, output_path: str, asy_filename: str = None) -> bool:
        """Generate SVG from Asymptote code"""
        temp_asy_path = None
        permanent_asy_path = None
        try:
            # Post-process the ASY code first
            asy_code = self.post_process_asy_code(asy_code)
            
            # Check if user code contains "import olympiad"
            # This check should be done after post-processing
            user_has_olympiad = 'import olympiad' in asy_code
            
            # Create output directory
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Remove .svg extension if present (Asymptote will add it)
            if output_path.endswith('.svg'):
                output_path_without_ext = output_path[:-4]
            else:
                output_path_without_ext = output_path
            
            # The actual output file will have .svg extension
            actual_output_path = output_path_without_ext + '.svg'
            
            # 如果提供了文件名，保存为永久文件
            if asy_filename:
                permanent_asy_path = os.path.join(TEMP_ASY_DIR, asy_filename)
                with open(permanent_asy_path, 'w', encoding='utf-8') as f:
                    # Add necessary imports and settings in the correct order
                    f.write("\n// Import modules in correct order\n")
                    # Only import graph if user code doesn't have olympiad
                    if not user_has_olympiad:
                        f.write("import graph;\n")
                    f.write("import geometry;\n")
                    f.write("import cse5;\n")
                    #f.write("pair IP(path a, path b) { return intersectionpoint(a, b); };\n")
                    f.write("pen pathpen;\n")
                    f.write("pen pointpen;\n")
                    f.write("size(300);\n")  # Set default size
                    f.write("\n// User code starts here\n")
                    f.write(asy_code)
                self.logger.info(f"Saved ASY code to permanent file: {permanent_asy_path}")
            
            # Create temporary asy file for compilation
            with tempfile.NamedTemporaryFile(mode='w', suffix='.asy', 
                                           dir=self.temp_dir, delete=False) as f:
                # Add necessary imports and settings in the correct order
                # f.write("import settings;\n")
                # f.write("settings.outformat = \"svg\";\n")
                # f.write("settings.render = 0;\n")
                # f.write("size(200);\n")  # Set default size
                # f.write("defaultpen(linewidth(0.8));\n")  # Set default pen
                # f.write("defaultpen(fontsize(10pt));\n")  # Set default font size
                f.write("\n// Import modules in correct order\n")
                # Only import graph if user code doesn't have olympiad
                if not user_has_olympiad:
                    f.write("import graph;\n")
                f.write("import geometry;\n")
                f.write("import cse5;\n")
                # f.write("pair IP(path a, path b) { return intersectionpoint(a, b); };\n")
                #f.write("unitsize(50);\n")#遇到train_geometry_6116_problem_0.asy会报错
                f.write("pen pathpen;\n")
                f.write("pen pointpen;\n")
                #f.write("size(5cm);\n")  # Set default size
                f.write("size(300);\n")  # Set default size
                f.write("\n// User code starts here\n")
                
                # Fix common issues in the code
                # asy_code_fixed = self._fix_common_asy_issues(asy_code)
                # f.write(asy_code_fixed)
                f.write(asy_code)
                temp_asy_path = f.name
            
            # Execute asymptote command with increased timeout
            # Use output path WITHOUT .svg extension - Asymptote will add it
            cmd = ['asy', '-f', 'svg', '-o', output_path_without_ext, temp_asy_path]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=90)
            
            # Check if SVG file was created (check the actual path with .svg)
            if os.path.exists(actual_output_path) and os.path.getsize(actual_output_path) > 0:
                if result.returncode != 0:
                    # File created but with non-zero exit code (warnings)
                    if result.stderr:
                        self.logger.debug(f"Asymptote warnings for {actual_output_path}: {result.stderr}")
                    else:
                        self.logger.debug(f"Asymptote returned exit code {result.returncode} but created {actual_output_path}")
                self.logger.info(f"Successfully generated SVG: {actual_output_path}")
                return True
            else:
                # File not created - this is a real error
                error_msg = result.stderr if result.stderr else f"Exit code {result.returncode}, no error message"
                self.logger.error(f"Asymptote failed to create {actual_output_path}: {error_msg}")
                return False
                
        except subprocess.TimeoutExpired:
            self.logger.error(f"Asymptote timeout for {output_path}")
            return False
        except Exception as e:
            self.logger.error(f"Error generating SVG {output_path}: {str(e)}")
            return False
        finally:
            # 屏蔽删除临时文件的代码 - 保留临时文件以便调试
            # Clean up temporary file
            # if temp_asy_path and os.path.exists(temp_asy_path):
            #     try:
            #         os.unlink(temp_asy_path)
            #     except:
            #         pass
            if temp_asy_path:
                self.logger.debug(f"Temporary ASY file preserved: {temp_asy_path}")
    
    def _fix_common_asy_issues(self, asy_code: str) -> str:
        """Fix common issues in Asymptote code"""
        # Fix xaxis/yaxis calls with arrow parameters
        # Pattern: xaxis(..., EndArrow) or yaxis(..., EndArrow)
        asy_code = re.sub(
            r'(xaxis|yaxis)\s*\(([^)]+),\s*(EndArrow|BeginArrow|Arrows|Arrow)\s*\)',
            r'\1(\2, arrow=\3)',
            asy_code
        )
        
        # Fix integer arguments in axis functions (convert to real)
        # This needs to be done BEFORE the arrow fix to handle cases like xaxis("$x$",0,12,EndArrow)
        def fix_axis_numbers(match):
            func = match.group(1)
            args = match.group(2)
            # Split arguments carefully, handling quoted strings
            parts = []
            current = []
            in_quotes = False
            paren_depth = 0
            
            for char in args:
                if char == '"' and (not current or current[-1] != '\\'):
                    in_quotes = not in_quotes
                elif char == '(' and not in_quotes:
                    paren_depth += 1
                elif char == ')' and not in_quotes:
                    paren_depth -= 1
                elif char == ',' and not in_quotes and paren_depth == 0:
                    parts.append(''.join(current).strip())
                    current = []
                    continue
                current.append(char)
            if current:
                parts.append(''.join(current).strip())
            
            # Process each part
            new_parts = []
            for part in parts:
                # Check if this part is a plain integer
                if re.match(r'^-?\d+$', part):
                    new_parts.append(f"{part}.0")
                else:
                    new_parts.append(part)
            
            return f"{func}({', '.join(new_parts)})"
        
        asy_code = re.sub(
            r'(xaxis|yaxis)\s*\(([^)]+)\)',
            fix_axis_numbers,
            asy_code
        )
        
        # Fix font issues - replace non-standard fonts
        # asy_code = asy_code.replace('font("Latin")', 'font("roman")')
        # asy_code = asy_code.replace('"Latin"', '"roman"')
        
        # Fix common import issues - remove duplicate imports since we import them in header
        lines = asy_code.split('\n')
        filtered_lines = []
        for line in lines:
            # Skip import statements that we already have in header
            if re.match(r'^\s*import\s+(graph|olympiad|cse5|geometry)\s*;', line):
                continue
            filtered_lines.append(line)
        asy_code = '\n'.join(filtered_lines)
        
        return asy_code
                    
    def test_asymptote_installation(self) -> bool:
        """Test if Asymptote is properly installed"""
        try:
            result = subprocess.run(['asy', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                self.logger.info(f"Asymptote version: {result.stdout.strip()}")
                return True
            else:
                self.logger.error("Asymptote not found or not working properly")
                return False
        except Exception as e:
            self.logger.error(f"Error testing Asymptote installation: {str(e)}")
            return False

class MathDatasetProcessor:
    def __init__(self, svg_generator):
        self.svg_generator = svg_generator
        self.asy_pattern = re.compile(r'\[asy\](.*?)\[/asy\]', re.DOTALL)
        self.logger = logging.getLogger('asy_converter')  # Use custom logger
        
    def extract_asy_blocks(self, text: str) -> List[str]:
        """Extract all [asy]...[/asy] blocks from text"""
        matches = self.asy_pattern.findall(text)
        return [match.strip() for match in matches]
    
    def process_text_asy_blocks(self, text: str, file_prefix: str, 
                               svg_output_dir: str, block_type: str) -> Tuple[str, List[Dict]]:
        """Process ASY blocks in text and replace with img tags"""
        asy_blocks = self.extract_asy_blocks(text)
        if not asy_blocks:
            return text, []
        
        result_text = text
        failed_conversions = []
        
        for i, asy_code in enumerate(asy_blocks):
            # Don't add .svg extension here - Asymptote will add it
            svg_filename = f"{file_prefix}_{block_type}_{i}"
            svg_filename_with_ext = f"{svg_filename}.svg"  # For display/reference
            asy_filename = f"{file_prefix}_{block_type}_{i}.asy"
            svg_path = os.path.join(svg_output_dir, svg_filename)  # Without .svg
            relative_path = f"/images/problems/{svg_filename_with_ext}"  # With .svg for HTML
            
            # Generate SVG (pass path without .svg extension)
            success = self.svg_generator.generate_svg(asy_code, svg_path, asy_filename)
            
            if success:
                # Replace with img tag
                img_tag = f'<img src="{relative_path}" alt="Asymptote diagram" />'
                # Replace only the first matching asy block
                result_text = self.asy_pattern.sub(img_tag, result_text, count=1)
                self.logger.debug(f"Converted ASY block to {svg_filename_with_ext}")
            else:
                failed_conversions.append({
                    'file_prefix': file_prefix,
                    'block_type': block_type,
                    'block_index': i,
                    'asy_code': asy_code,
                    'asy_filename': asy_filename,
                    'expected_svg': svg_filename_with_ext
                })
                self.logger.warning(f"Failed to convert ASY block in {file_prefix}_{block_type}_{i}")
        
        return result_text, failed_conversions
    
    def process_json_file(self, json_path: Path, file_prefix: str, 
                         svg_output_dir: str, backup: bool = True) -> Dict:
        """Process a single JSON file"""
        stats = {
            'file_path': str(json_path),
            'processed': False,
            'problem_asy_blocks': 0,
            'solution_asy_blocks': 0,
            'failed_conversions': [],
            'error': None
        }
        
        try:
            # Backup original file
            if backup:
                backup_path = json_path.with_suffix('.json.backup')
                if not backup_path.exists():
                    shutil.copy2(json_path, backup_path)
            
            # Read JSON data
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            modified = False
            
            # Process problem field
            if 'problem' in data:
                original_text = data['problem']
                asy_blocks = self.extract_asy_blocks(original_text)
                stats['problem_asy_blocks'] = len(asy_blocks)  # Count ALL blocks
                
                if asy_blocks:  # Only process if there are blocks
                    processed_text, failed = self.process_text_asy_blocks(
                        original_text, file_prefix, svg_output_dir, 'problem'
                    )
                    if processed_text != original_text:
                        data['problem'] = processed_text
                        modified = True
                    stats['failed_conversions'].extend(failed)
            
            # Process solution field
            if 'solution' in data:
                original_text = data['solution']
                asy_blocks = self.extract_asy_blocks(original_text)
                stats['solution_asy_blocks'] = len(asy_blocks)  # Count ALL blocks
                
                if asy_blocks:  # Only process if there are blocks
                    processed_text, failed = self.process_text_asy_blocks(
                        original_text, file_prefix, svg_output_dir, 'solution'
                    )
                    if processed_text != original_text:
                        data['solution'] = processed_text
                        modified = True
                    stats['failed_conversions'].extend(failed)
            
            # Save modified JSON
            if modified:
                with open(json_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                self.logger.info(f"Updated {json_path}")
            
            stats['processed'] = True
            
        except Exception as e:
            stats['error'] = str(e)
            self.logger.error(f"Error processing {json_path}: {str(e)}")
        
        return stats
    
    def get_file_prefix(self, json_path: Path, dataset_root: str) -> str:
        """Generate file prefix based on path structure"""
        try:
            # Get relative path from dataset_root
            rel_path = json_path.relative_to(Path(dataset_root))
            parts = rel_path.parts
            
            if len(parts) >= 3:
                split = parts[0]  # train or test
                category = parts[1]  # algebra, geometry, etc.
                filename = parts[2]  # 861.json
                file_id = Path(filename).stem  # 861
                return f"{split}_{category}_{file_id}"
            elif len(parts) >= 2:
                # Handle files directly in test/train folders
                split = parts[0]
                filename = parts[1]
                file_id = Path(filename).stem
                return f"{split}_{file_id}"
            else:
                # Handle files in root
                return json_path.stem
        except Exception as e:
            self.logger.error(f"Error generating file prefix for {json_path}: {e}")
            return f"unknown_{json_path.stem}"

def setup_logging() -> Tuple[logging.Logger, logging.Logger]:
    """Setup logging configuration"""
    # Create logs directory
    os.makedirs(LOGS_DIR, exist_ok=True)
    
    # Configure root logger to only show CRITICAL messages
    logging.basicConfig(
        level=logging.CRITICAL,  # Changed to suppress root logger messages
        format='%(levelname)s:%(name)s:%(message)s'
    )
    
    # Main logger configuration
    main_logger = logging.getLogger('asy_converter')
    main_logger.setLevel(logging.DEBUG)  # Changed to DEBUG to capture all messages
    main_logger.handlers = []  # Clear any existing handlers
    
    # Failure logger configuration
    failure_logger = logging.getLogger('asy_failures')
    failure_logger.setLevel(logging.WARNING)
    failure_logger.handlers = []  # Clear any existing handlers
    
    # Log format
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Main log handler
    main_handler = logging.FileHandler(
        os.path.join(LOGS_DIR, LOG_FILENAME), 
        encoding='utf-8'
    )
    main_handler.setLevel(logging.DEBUG)
    main_handler.setFormatter(formatter)
    
    # Console handler - only show INFO and above
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    
    # Failure log handler
    failure_handler = logging.FileHandler(
        os.path.join(LOGS_DIR, FAILURE_LOG_FILENAME),
        encoding='utf-8'
    )
    failure_handler.setLevel(logging.WARNING)
    failure_handler.setFormatter(formatter)
    
    main_logger.addHandler(main_handler)
    main_logger.addHandler(console_handler)
    failure_logger.addHandler(failure_handler)
    
    return main_logger, failure_logger

def find_json_files(dataset_root: str) -> List[Path]:
    """Find all JSON files in the dataset"""
    json_files = []
    root_path = Path(dataset_root)
    
    # Get custom logger
    logger = logging.getLogger('asy_converter')
    
    if root_path.exists():
        # Look for JSON files in the root
        for json_file in root_path.glob('*.json'):
            json_files.append(json_file)
        
        # Check train and test directories
        for split in ['train', 'test']:
            split_dir = root_path / split
            if split_dir.exists():
                # Check for JSON files directly in split directory
                for json_file in split_dir.glob('*.json'):
                    json_files.append(json_file)
                # Check subdirectories
                for category_dir in split_dir.iterdir():
                    if category_dir.is_dir():
                        for json_file in category_dir.glob('*.json'):
                            json_files.append(json_file)
            else:
                logger.warning(f"Split directory not found: {split_dir}")
    
    return sorted(json_files)

def process_single_file(args: tuple) -> Dict:
    """Process a single JSON file (for multiprocessing)"""
    json_path, dataset_root, svg_output_dir = args
    
    # Each process creates its own processor instance
    svg_generator = AsymptoteSVGGenerator()
    processor = MathDatasetProcessor(svg_generator)
    
    file_prefix = processor.get_file_prefix(json_path, dataset_root)
    return processor.process_json_file(json_path, file_prefix, svg_output_dir, BACKUP_ORIGINAL)

def log_processing_statistics(stats_list: List[Dict], main_logger: logging.Logger, 
                            failure_logger: logging.Logger):
    """Log processing statistics and failures"""
    total_files = len(stats_list)
    successful_files = sum(1 for s in stats_list if s['processed'])
    failed_files = total_files - successful_files
    
    total_problem_blocks = sum(s['problem_asy_blocks'] for s in stats_list)
    total_solution_blocks = sum(s['solution_asy_blocks'] for s in stats_list)
    total_failed_conversions = sum(len(s['failed_conversions']) for s in stats_list)
    
    # Main statistics log
    main_logger.info("="*60)
    main_logger.info("PROCESSING SUMMARY")
    main_logger.info("="*60)
    main_logger.info(f"Total JSON files processed: {total_files}")
    main_logger.info(f"Successfully processed: {successful_files}")
    main_logger.info(f"Failed to process: {failed_files}")
    main_logger.info(f"Total ASY blocks in problems: {total_problem_blocks}")
    main_logger.info(f"Total ASY blocks in solutions: {total_solution_blocks}")
    main_logger.info(f"Total failed ASY conversions: {total_failed_conversions}")
    main_logger.info(f"ASY files saved to: {TEMP_ASY_DIR}")
    main_logger.info("="*60)
    
    # Log failed files
    if failed_files > 0:
        main_logger.error("Files that failed to process:")
        for stats in stats_list:
            if not stats['processed']:
                main_logger.error(f"  {stats['file_path']}: {stats['error']}")
    
    # Log failed ASY conversions
    if total_failed_conversions > 0:
        failure_logger.warning("="*60)
        failure_logger.warning("FAILED ASY CONVERSIONS")
        failure_logger.warning("="*60)
        
        for stats in stats_list:
            for failure in stats['failed_conversions']:
                failure_logger.warning(f"File: {stats['file_path']}")
                failure_logger.warning(f"Block: {failure['file_prefix']}_{failure['block_type']}_{failure['block_index']}")
                failure_logger.warning(f"Expected SVG: {failure['expected_svg']}")
                failure_logger.warning(f"ASY file: {failure.get('asy_filename', 'N/A')}")
                failure_logger.warning(f"ASY Code:\n{failure['asy_code']}")
                failure_logger.warning("-" * 40)

def main():
    parser = argparse.ArgumentParser(description='Convert ASY diagrams to SVG in MATH dataset')
    parser.add_argument('--dataset-root', default=DATASET_ROOT, 
                       help=f'Root directory of MATH dataset (default: {DATASET_ROOT})')
    parser.add_argument('--output-dir', default=SVG_OUTPUT_DIR, 
                       help=f'Output directory for SVG files (default: {SVG_OUTPUT_DIR})')
    parser.add_argument('--max-workers', type=int, default=MAX_WORKERS,
                       help='Maximum number of parallel workers')
    parser.add_argument('--no-parallel', action='store_true',
                       help='Disable parallel processing')
    
    args = parser.parse_args()
    
    # Verify paths exist
    if not os.path.exists(args.dataset_root):
        print(f"Error: Dataset root directory not found: {args.dataset_root}")
        print("Please make sure you have the MATH dataset in the data/ directory")
        sys.exit(1)
    
    # Setup logging
    main_logger, failure_logger = setup_logging()
    main_logger.info(f"Starting ASY to SVG conversion process")
    main_logger.info(f"Project root: {PROJECT_ROOT}")
    main_logger.info(f"Dataset root: {args.dataset_root}")
    main_logger.info(f"Output directory: {args.output_dir}")
    main_logger.info(f"Temp ASY directory: {TEMP_ASY_DIR}")
    
    # Check Asymptote installation
    svg_generator = AsymptoteSVGGenerator()
    if not svg_generator.test_asymptote_installation():
        main_logger.error("Asymptote is not properly installed. Please install Asymptote first.")
        sys.exit(1)
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Find all JSON files
    json_files = find_json_files(args.dataset_root)
    main_logger.info(f"Found {len(json_files)} JSON files to process")
    
    if not json_files:
        main_logger.error("No JSON files found to process")
        main_logger.error(f"Searched in: {args.dataset_root}")
        main_logger.error("Directory structure should be: data/MATH/test/ and data/MATH/train/")
        sys.exit(1)
    
    # Process files
    stats_list = []
    
    if args.no_parallel:
        # Sequential processing
        main_logger.info("Processing files sequentially...")
        for json_path in json_files:
            stats = process_single_file((json_path, args.dataset_root, args.output_dir))
            stats_list.append(stats)
            if len(stats_list) % 10 == 0:
                main_logger.info(f"Processed {len(stats_list)}/{len(json_files)} files")
    else:
        # Parallel processing
        main_logger.info(f"Processing files with {args.max_workers} workers...")
        file_args = [(json_path, args.dataset_root, args.output_dir) for json_path in json_files]
        
        with ProcessPoolExecutor(max_workers=args.max_workers) as executor:
            futures = {executor.submit(process_single_file, arg): arg for arg in file_args}
            
            for future in as_completed(futures):
                try:
                    stats = future.result()
                    stats_list.append(stats)
                    if len(stats_list) % 10 == 0:
                        main_logger.info(f"Processed {len(stats_list)}/{len(json_files)} files")
                except Exception as e:
                    main_logger.error(f"Error processing file: {e}")
    
    # Log statistics
    log_processing_statistics(stats_list, main_logger, failure_logger)
    
    main_logger.info("ASY to SVG conversion completed")
    main_logger.info(f"Main log: {os.path.join(LOGS_DIR, LOG_FILENAME)}")
    main_logger.info(f"Failure log: {os.path.join(LOGS_DIR, FAILURE_LOG_FILENAME)}")

if __name__ == "__main__":
    main()
