"use client"
import React, { createContext, useContext, useState } from "react"
import { cn } from "@/lib/utils"

interface DialogContextType {
  open: boolean
  setOpen: (open: boolean) => void
}

const DialogContext = createContext<DialogContextType | undefined>(undefined)

function Dialog({ 
  children, 
  open, 
  onOpenChange 
}: { 
  children: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void 
}) {
  const [internalOpen, setInternalOpen] = useState(false)
  const isOpen = open !== undefined ? open : internalOpen
  const setOpen = onOpenChange || setInternalOpen
  
  return (
    <DialogContext.Provider value={{ open: isOpen, setOpen }}>
      {children}
    </DialogContext.Provider>
  )
}

function DialogTrigger({ 
  children,
  className
}: { 
  children: React.ReactNode
  className?: string
}) {
  const context = useContext(DialogContext)
  if (!context) throw new Error('DialogTrigger must be used within Dialog')
  
  return (
    <button 
      className={className}
      onClick={() => context.setOpen(true)}
    >
      {children}
    </button>
  )
}

function DialogContent({ 
  children,
  className
}: { 
  children: React.ReactNode
  className?: string
}) {
  const context = useContext(DialogContext)
  if (!context) throw new Error('DialogContent must be used within Dialog')
  
  if (!context.open) return null
  
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50" 
        onClick={() => context.setOpen(false)}
      />
      {/* Content */}
      <div className={cn(
        "relative bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-lg",
        className
      )}>
        {children}
      </div>
    </div>
  )
}

function DialogHeader({ 
  children,
  className
}: { 
  children: React.ReactNode
  className?: string
}) {
  return (
    <div className={cn("flex flex-col space-y-1.5 text-center sm:text-left", className)}>
      {children}
    </div>
  )
}

function DialogTitle({ 
  children,
  className
}: { 
  children: React.ReactNode
  className?: string
}) {
  return (
    <h2 className={cn("text-lg font-semibold leading-none tracking-tight", className)}>
      {children}
    </h2>
  )
}

function DialogDescription({ 
  children,
  className
}: { 
  children: React.ReactNode
  className?: string
}) {
  return (
    <p className={cn("text-sm text-gray-500", className)}>
      {children}
    </p>
  )
}

function DialogClose({ 
  children,
  className
}: { 
  children: React.ReactNode
  className?: string
}) {
  const context = useContext(DialogContext)
  if (!context) throw new Error('DialogClose must be used within Dialog')
  
  return (
    <button 
      className={className}
      onClick={() => context.setOpen(false)}
    >
      {children}
    </button>
  )
}

function DialogFooter({ 
  children,
  className
}: { 
  children: React.ReactNode
  className?: string
}) {
  return (
    <div className={cn(
      "flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",
      className
    )}>
      {children}
    </div>
  )
}
// Export all components
export {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogClose,
  DialogFooter, // Add this line
}