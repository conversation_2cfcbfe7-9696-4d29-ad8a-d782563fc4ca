"use client"

import { useEffect, useState } from "react"

export interface Toast {
  id: string
  title?: string
  description?: string
  action?: React.ReactNode
  variant?: "default" | "destructive"
}

const toasts: Toast[] = []
const listeners: Array<(toasts: Toast[]) => void> = []

const addToast = (toast: Omit<Toast, "id">) => {
  const id = Math.random().toString(36).substr(2, 9)
  toasts.push({ ...toast, id })
  listeners.forEach((listener) => listener([...toasts]))
  
  // 自动移除 toast
  setTimeout(() => {
    const index = toasts.findIndex((t) => t.id === id)
    if (index > -1) {
      toasts.splice(index, 1)
      listeners.forEach((listener) => listener([...toasts]))
    }
  }, 5000)
}

const removeToast = (id: string) => {
  const index = toasts.findIndex((t) => t.id === id)
  if (index > -1) {
    toasts.splice(index, 1)
    listeners.forEach((listener) => listener([...toasts]))
  }
}

export const toast = Object.assign(
  (props: Omit<Toast, "id">) => {
    addToast(props)
  },
  {
    success: (props: Omit<Toast, "id" | "variant">) => addToast({ ...props, variant: "default" }),
    error: (props: Omit<Toast, "id" | "variant">) => addToast({ ...props, variant: "destructive" }),
    info: (props: Omit<Toast, "id" | "variant">) => addToast({ ...props, variant: "default" }),
  }
)

export function Toaster() {
  const [toastList, setToastList] = useState<Toast[]>([])

  useEffect(() => {
    const listener = (toasts: Toast[]) => setToastList(toasts)
    listeners.push(listener)
    
    return () => {
      const index = listeners.indexOf(listener)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }, [])

  return (
    <div className="fixed top-4 right-4 z-50 flex flex-col gap-2">
      {toastList.map((toast) => (
        <div
          key={toast.id}
          className={`
            min-w-80 rounded-lg border p-4 shadow-lg transition-all duration-300
            ${toast.variant === "destructive" 
              ? "bg-red-50 border-red-200 text-red-900" 
              : "bg-white border-gray-200 text-gray-900"
            }
          `}
        >
          {toast.title && (
            <div className="font-semibold mb-1">{toast.title}</div>
          )}
          {toast.description && (
            <div className="text-sm opacity-90">{toast.description}</div>
          )}
          {toast.action && (
            <div className="mt-2">{toast.action}</div>
          )}
          <button
            onClick={() => removeToast(toast.id)}
            className="absolute top-2 right-2 text-gray-400 hover:text-gray-600"
          >
            ×
          </button>
        </div>
      ))}
    </div>
  )
}