// components/latex-renderer.tsx (治本后的最终代码)

'use client';

import React from 'react';
import { MathJax, MathJaxContext } from 'better-react-mathjax';

// MathJax配置可以保持不变，但建议把所有包都在tex里也声明一下
const mathJaxConfig = {
  loader: {
    // load: ['[tex]/html', '[tex]/ams', '[tex]/array', '[tex]/colortbl']
    load: ['[tex]/html']
  },
  tex: {
    // 建议：在这里也加上包，确保万无一失
    // packages: { '[+]': ['html', 'ams', 'array', 'colortbl'] },
    packages: { '[+]': ['html'] },
    inlineMath: [
      ['$', '$'],
      ['\\(', '\\)']
    ],
    displayMath: [
      ['$$', '$$'],
      ['\\[', '\\]']
    ]
  }
};

// ----------------------------------------------------
// 1. 删除整个 preprocessMathText 函数，我们不再需要它了
// ----------------------------------------------------

// MathJax渲染组件
export const MathJaxRenderer = ({ math }: { math: string }) => {
  // 2. 直接使用原始的 math 字符串
  return (
    // 注意: 为了获得最佳性能，这个Context应该在App的更高层级提供
    // 这里为了演示，暂时保留。请看下面的优化建议。
    <MathJaxContext config={mathJaxConfig}>
      <MathJax>{math}</MathJax>
    </MathJaxContext>
  );
};

// 混合内容渲染组件
export const MixedContent = ({ content }: { content: string }) => {
  if (!content) return null;

  // Check for HTML tables first - they need special handling
  const hasHTMLTables = /<table\s[^>]*>[\s\S]*?<\/table>/i.test(content);
  const hasImages = /<img\s+[^>]*>/i.test(content);
  
  if (hasHTMLTables) {
    // Handle HTML tables by splitting content and rendering tables as HTML, other parts through MathJax
    const parseContentWithTables = (text: string) => {
      // Find all HTML tables
      const tableRegex = /<table\s[^>]*>[\s\S]*?<\/table>/gi;
      const parts = text.split(tableRegex);
      const tableTags = text.match(tableRegex) || [];
      
      const result: React.ReactNode[] = [];
      let keyIndex = 0;

      parts.forEach((part, index) => {
        if (part.trim()) {
          // Check if this part contains images and handle accordingly
          if (/<img\s+[^>]*>/i.test(part)) {
            // Parse images within this text part
            const imgTagRegex = /<img\s+[^>]*>/gi;
            const textParts = part.split(imgTagRegex);
            const imgTags = part.match(imgTagRegex) || [];
            
            textParts.forEach((textPart, imgIndex) => {
              if (textPart.trim()) {
                result.push(<MathJax key={`text-${keyIndex++}`}>{textPart}</MathJax>);
              }
              if (imgTags[imgIndex]) {
                const imgTag = imgTags[imgIndex];
                const srcMatch = imgTag.match(/src=["']([^"']+)["']/);
                const altMatch = imgTag.match(/alt=["']([^"']*)["']/);
                if (srcMatch) {
                  result.push(
                    <img
                      key={`img-${keyIndex++}`}
                      src={srcMatch[1]}
                      alt={altMatch ? altMatch[1] : 'Math diagram'}
                      className="max-w-full h-auto my-4 mx-auto block"
                    />
                  );
                }
              }
            });
          } else {
            // Regular text part, render through MathJax
            result.push(<MathJax key={`text-${keyIndex++}`}>{part}</MathJax>);
          }
        }
        
        // Add HTML table as raw HTML
        if (tableTags[index]) {
          result.push(
            <div 
              key={`table-${keyIndex++}`} 
              dangerouslySetInnerHTML={{ __html: tableTags[index] }}
              className="my-4"
            />
          );
        }
      });
      
      return result;
    };

    return (
      <div className="mixed-content">
        <MathJaxContext config={mathJaxConfig}>
          {parseContentWithTables(content)}
        </MathJaxContext>
      </div>
    );
  }
  
  else if (hasImages) {
    // Handle images as before
    const parseContentWithImages = (text: string) => {
      const imgTagRegex = /<img\s+[^>]*>/gi;
      const parts = text.split(imgTagRegex);
      const imgTags = text.match(imgTagRegex) || [];
      
      const result: React.ReactNode[] = [];
      let keyIndex = 0;

      parts.forEach((part, index) => {
        if (part) {
          result.push(<MathJax key={`text-${keyIndex++}`}>{part}</MathJax>);
        }
        if (imgTags[index]) {
          const imgTag = imgTags[index];
          const srcMatch = imgTag.match(/src=["']([^"']+)["']/);
          const altMatch = imgTag.match(/alt=["']([^"']*)["']/);
          if (srcMatch) {
            result.push(
              <img
                key={`img-${keyIndex++}`}
                src={srcMatch[1]}
                alt={altMatch ? altMatch[1] : 'Math diagram'}
                className="max-w-full h-auto my-4 mx-auto block"
              />
            );
          }
        }
      });
      return result;
    };

    return (
      <div className="mixed-content">
        <MathJaxContext config={mathJaxConfig}>
          {parseContentWithImages(content)}
        </MathJaxContext>
      </div>
    );

  } else {
    // No images or tables, simple case
    return (
      <div className="mixed-content">
        <MathJaxContext config={mathJaxConfig}>
          <MathJax>{content}</MathJax>
        </MathJaxContext>
      </div>
    );
  }
};


// 向后兼容的Latex组件
export const Latex = ({ children }: { children: string }) => {
  // 这个组件会自动从 MathJaxRenderer 的修改中受益
  return <MathJaxRenderer math={children} />;
};